# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__accepted_applications_count
msgid "# Accepted Offers"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applications_count
msgid "# Offers"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__refused_applications_count
msgid "# Refused Offers"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "1 Meeting"
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Click to view</b> the application."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Did you apply by sending an email?</b> Check incoming applications."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Drag this card</b>, to qualify him for a first interview."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"<div><b>Try to send an email</b> to the applicant.</div><div><i>Tips: All "
"emails sent or received are saved in the history here</i>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alternativ\" "
"title=\"Alternativ\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"
msgstr "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Sənədlər/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid ""
"<span class=\"mx-2\" style=\"padding-top: 1px; padding-bottom: "
"1px;\">to</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr "<span class=\"o_stat_text\">İzləyicilər</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span invisible=\"address_id\" class=\"oe_read_only\">Remote</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"<span invisible=\"not is_warning_visible\">\n"
"                                <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                                </span>\n"
"                                <span class=\"text-danger\">\n"
"                                    All applications will lose their hired date and hired status.\n"
"                                </span>\n"
"                            </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_expected_extra\"> + </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_proposed_extra\"> + </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>New</span>"
msgstr "<span>Yeni</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Mühasibat uçotu</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>View</span>"
msgstr "<span>Baxın</span>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application for the job\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Experienced Developer</strong></a>\" at <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <p>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                        <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    </p>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <t t-if=\"object.user_id\">\n"
"                    You will soon be contacted by:<br/>\n"
"                    <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                    <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                    <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    <br/><br/>\n"
"                </t>\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    </t>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_not_interested
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Dear,<br/><br/>\n"
"                We would like to thank you for your interest and your time.<br/>\n"
"                We wish you all the best in your future endeavors.\n"
"                <br/><br/>\n"
"                Best<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> team.  We\n"
"                wanted to let you know that, although your resume is\n"
"                competitive, our hiring team reviewed your application\n"
"                and <b>did not select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can make\n"
"                mistakes. Do not hesitate to reply to this email if you\n"
"                think we made a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Bu qoşma ad üzrə yeni qeydlər yaradarkən susmaya görə dəyərləri təmin etmək "
"üçün qiymətləndiriləcək Python lüğəti."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__active
msgid "Active"
msgstr "Aktiv"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_activities
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_overdue
msgid "Activities Overdue"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_today
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities Today"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_plan
msgid "Activity Plan"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_plan
msgid "Activity Plans"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr "Fəaliyyət Növləri"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Language Test\", \"Prepare Offer\", ...)"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr "İşəqəbul prosesində yeni mərhələ əlavə et"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr "Yeni etiket əlavə edin"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr "Qoşma Ad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr "Kontakt Təhlükəsizliyinə Alternativ Ad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain_id
msgid "Alias Domain"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias Domain Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_full_name
msgid "Alias Email"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr "Təxəllüs ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr "Alternativ Ad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_status
msgid "Alias Status"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr "Alternativ Model adı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__all_application_count
msgid "All Application Count"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr "Bütün Tətbiqlər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Analysis"
msgstr "Təhlil"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_applicant_new
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applicant_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "Applicant"
msgstr "Namizəd"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr "Namizədin Elmi Dərəcəsi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_emails
msgid "Applicant Emails"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr "Namizəd İşə götürüldü"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_notes
msgid "Applicant Notes"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_properties_definition
msgid "Applicant Properties"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr "Namizəd Mərhələsi Dəyişdirildi"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr "Namizəd yaradıldı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_without_email
msgid "Applicant(s) not having email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_activity
msgid "Applicants"
msgstr "Namizədlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_hired
msgid "Applicants Hired"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached résumé are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application"
msgstr "Ərizə "

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr "Ərizələrin Sayı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_status
msgid "Application Status"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr "Ərizə elektron məktubu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Application in Progress"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__applicant_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_applications
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_view_tree_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr "Ərizələr"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__email
msgid ""
"Applications received from this Email won't be linked to a contact.There "
"will be no email address set on the Applicant either."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
msgid "Applied on"
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Archive"
msgstr "Arxiv"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__archived
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Archived"
msgstr "Arxivləndi"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
msgid "Are you sure that you want to archive this job position?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr "Təyin edilmiş"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"At least one applicant doesn't have a email; you can't use send email "
"option."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Attach a file"
msgstr "Fay əlavə edin"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_ids
msgid "Attachments"
msgstr "Qoşmalar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__author_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__author_id
msgid "Author"
msgstr "Müəllif"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__availability
msgid "Availability"
msgstr "Əlçatanlıq"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr "Bakalavr Dərəcəsi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__is_blacklisted
msgid "Blacklist"
msgstr "Qara Siyahı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Qara siyahıya düşən telefon mobildir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Qara siyahıya düşən Telefon Telefondur"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Blocked"
msgstr "Bloklanıb"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body_has_template_value
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_bounce
msgid "Bounce"
msgstr "Sıçrayış edin"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_interviewer
msgid "By Job Positions"
msgstr "İşdəki mövqeyə görə"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"By setting an alias to a job position, emails sent to this address create "
"applications automatically. You can even use multiple trackers to get "
"statistics according to the source of the application: LinkedIn, Monster, "
"Indeed, etc."
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr "Təqvim Tədbiri"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "Kampaniya"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__can_edit_body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__candidate_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Candidate"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__user_id
msgid "Candidate Manager"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__candidate_properties_definition
msgid "Candidate Properties"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Candidate's Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__candidate_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_candidate
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Candidates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Candidates with the same email or phone or mobile"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_name
msgid "Candidates's Name"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr "Mərhələ və Qiymətləndirmə halları"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr "Ərizəçinin kateqoriyası"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Choose an application email."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr "Rəng İndeksi"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Company"
msgstr "Şirkət"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_congratulations
msgid "Confirmation email sent to all new job applications"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Contact Email"
msgstr "E-poçt ilə Əlaqə"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "Məzmun"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body
msgid "Contents"
msgstr "Məzmun"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr "Müqavilə Təklifi"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr "Müqavilə İmzalanıb"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Copy this email address, to paste it in your email composer, to apply."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""
"Bu kontakta aid çatdırıla bilməyən emaillərin sayını müəyyən edən "
"hesablayıcı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "Yaradın"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Create Employee"
msgstr "İşçi yarat"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr "Vəzifə yarat"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Create a Recruitment Activity Plan"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Create a new rule to process emails from specific job boards."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Create your first Job Position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Creation Date"
msgstr "Yaradılma Tarixi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Xüsusi Qaytarılmış Mesaj"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_from
msgid "Date From"
msgstr "Başlama Tarixi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_to
msgid "Date To"
msgstr "Bitmə tarixi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "Bağlanış Günləri"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "Açılış Günləri"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr "Defolt Dəyərlər"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid ""
"Define a regex: Extract the applicant's name from the email's subject or "
"body."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""
"Bu vəzifə üçün xüsusi müqavilə ünvanı müəyyən et. Onu boş saxladığınız "
"halda, insan resursları tənzimləmələrindəki susmaya görə email ünvanı "
"istifadə olunacaq"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"                    qualification call, first interview, second interview, refused,\n"
"                    hired."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__type_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
msgid "Degree"
msgstr "Dərəcə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
msgid "Degree Name"
msgstr "Dərəcənin Adı"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr "Dərəcələr"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "Bağlamaq üçün Gecikmə"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr "Silin"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "Şöbə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr "Şöbə Meneceri"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "Şöbələr"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__name
msgid "Description"
msgstr "Təsvir"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Details"
msgstr "Ətraflı Məlumat"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr "Daycest"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Digitize your résumé to extract name and email automatically."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Directly"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Directly Available"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr "Ləğv edin"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_applicant_cv_display
msgid "Display CV on application form"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Display résumé on application form"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Əgər giriş edə bilmirsinizsə, bu məlumatı istifadəçinin günlük emaili üçün "
"buraxın"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr "Doktorluq Dərəcəsi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr "Sənədin Sayı"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Documents"
msgstr "Sənədlər"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_1
msgid "Does not fit the job requirements"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"                is different according to the job position."
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_6
msgid "Duplicate"
msgstr "Təkrar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates
msgid "Duplicates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates_count
msgid "Duplicates Count"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__email
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Email"
msgstr "Elektron poçt"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr "Alternativ Email Adı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Email Template"
msgstr "Email Şablonu"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""
"Bu iş mövqeyi üçün email təxəllüsü. Yeni emaillər avtomatik olaraq  bu iş "
"mövqeyi üçün yeni namizədlər yaradacaq."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_cc
msgid "Email cc"
msgstr "Elektron Poçt cc"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid "Email template must be selected to send a mail"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_platforms
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_emails
msgid "Emails"
msgstr "Emaillər"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee"
msgstr "İşçi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__emp_is_active
msgid "Employee Active"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_name
msgid "Employee Name"
msgstr "İşçinin Adı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_hired_template
msgid "Employee created:"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee linked to the candidate."
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_employees
msgid "Employees"
msgstr "İşçilər"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_contract_type
msgid "Employment Types"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__priority
msgid "Evaluation"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__3
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__3
msgid "Excellent"
msgstr "Mükəmməl"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Expected"
msgstr "Ehtimal edilən"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr "Ehtimal edilən İşçi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr "Ehtimal Olunan Əlavə Əmək Haqqı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "Genişləndirilmiş Filtrlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__extended_interviewer_ids
msgid "Extended Interviewer"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__favorite_user_ids
msgid "Favorite User"
msgstr "Seçilən istifadəçi "

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
" Filterlənmiş telefon nömrəsinin saxlanması üçün istifadə olunan sahə. "
"Axtarışları və müqayisələri sürətləndirməyə kömək edir."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "File"
msgstr "Fayl"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr "İlk Müsahibə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Kanban"
msgstr "Kanbanda bağlanmış"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "Folded in Recruitment Pipe:"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Generate Email"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_get_refuse_reason
msgid "Get Refuse Reason"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__1
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__1
msgid "Good"
msgstr "Əla"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr "Ölçü"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Great job! You hired a new colleague!"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__done
msgid "Green"
msgstr "Yaşıl"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr "Yaşıl Kanban leyblı"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__normal
msgid "Grey"
msgstr "Boz"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Boz Kanban leyblı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__group_applicant_cv_display
msgid "Group Applicant Cv Display"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Group By"
msgstr "Görə Qrupla"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__has_domain
msgid "Has Domain"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__has_message
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"Have you tried to <a>add skills to your job position</a> and search into the"
" Reserve ?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Hire Date"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__no_of_hired_employee
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid "Hired Stage"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Hiring Date"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Təxəllüsün olduğu əsas qeydin ID nömrəsi (məsələn: tapşırığın yaradılması "
"təxəllüsünün olduğu layihə)"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid ""
"If checked, this stage is used to determine the hire date of an applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""
"Təyin edilibsə, ərizəçi mərhələyə təyin edildiyi zaman, şablondan istifadə "
"edilərək, ismarış dərc ediləcək."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Qurulubsa, bu məzmun avtomatik olaraq standart mesaj yerinə icazəsiz "
"istifadəçilərə göndəriləcək."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__emp_is_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Aktiv sahə Yanlış olaraq təyin edilibsə, sizə ehtiyat qeydini silmədən "
"gizlətməyə icazə veriləcək."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""
"Aktiv sahə Yanlış olaraq təyin edilibsə, sizə işi silmədən gizlətməyə icazə "
"veriləcək."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Email ünvanı qara siyahıda olarsa, kontaktdakı şəxs artıq hər hansı bir "
"siyahıdan kütləvi mesaj qəbul etməyəcək."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Filterlənmiş telefon nömrəsi qara siyahıdadırsa, əlaqə daha heç bir "
"siyahıdan kütləvi poçt sms almayacaq."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "In Progress"
msgstr "Həyata Keçirilməkdə"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "In-App Purchases"
msgstr "Tətbiq Daxilində Satınalmalar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Incoming emails create applications automatically. Use it for direct "
"applications or when posting job offers on LinkedIn, Monster, etc."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Qara siyahıya alınmış  filterlənmiş telefon nömrəsinin mobil nömrə olub-"
"olmadığını göstərir. Modeldə həm mobil, həm də telefon sahəsi olduqda hansı "
"nömrənin qara siyahıya salındığını ayırd etməyə kömək edir."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Qara siyahıya alınmış filterlənmiş telefon nömrəsinin mobil nömrə olub-"
"olmadığını göstərir. Modeldə həm mobil, həm də telefon sahəsi olduqda hansı "
"nömrənin qara siyahıya salındığını ayırd etməyə kömək edir."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__industry_id
msgid "Industry"
msgstr "Sənaye"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr "İlkin İxtisaslaşma"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Internal notes..."
msgstr "Daxili qeydlər..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
msgid "Interview Forms"
msgstr "Müsahibə Formaları"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_interviewer
msgid "Interviewer"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__interviewer_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__interviewer_ids
msgid "Interviewers"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__is_mail_template_editor
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__is_favorite
msgid "Is Favorite"
msgstr "Sevimlidir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__is_warning_visible
msgid "Is Warning Visible"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__date_from
msgid ""
"Is set, update candidates availability once hired for that specific mission."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Job"
msgstr "Vəzifə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Job Applications"
msgstr "Vəzifə Müraciətləri"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_boards
msgid "Job Boards"
msgstr ""

#. module: hr_recruitment
#: model:utm.campaign,title:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr "Vəzifə Kampaniyası"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr "İş Yeri"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job_platform
msgid "Job Platforms"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Job Position"
msgstr "İş Mövqeyi"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr "Yaradılmış Vakansiya"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_job_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr "Yaradılmış Vakansiya"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_interviewer
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_jobs
msgid "Job Positions"
msgstr "Vakansiyalar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr "İşə Qəbul Haqqında Elan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__job_properties_definition
msgid "Job Properties"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Job Specific"
msgstr "Vəzifəyə Xas"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_5
msgid "Job already fulfilled"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "İşlər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Jobs - Recruitment Form"
msgstr "İşlər - İşə Qəbul Forması"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr "Kanban Blok Edilib"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr "Cari Kanban"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr "Kanban Metodu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr "Qüvvədə olan Kanban"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr "ƏEG İnsan Resursları İşə Qəbul üzrə Yeni Əməkdaşların Dəyəri"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__lang
msgid "Language"
msgstr "Dil"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Last Meeting"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr "Son Mərhələ"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Last Stage Update"
msgstr "Ən son Mərhələ Yeniləməsi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr "Gəlin vəzifə yaradaq."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"Let's create the position. An email will be setup for applications, and a "
"public job description, if you use the Website app."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let's have a look at how to <b>improve</b> your <b>hiring process</b>."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s create this new employee now."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s go back to the dashboard."
msgstr "Əsas Panelə qayıdaq "

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__linkedin_profile
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__linkedin_profile
msgid "LinkedIn Profile"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.action_load_demo_data
msgid "Load demo data"
msgstr "Demo tarix yüklə"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "Load sample data"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Load template"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__template_id
msgid "Mail Template"
msgstr "Məktub Şablonu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Əsas Əlavə"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Manager"
msgstr "Menecer"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr "Əsas Dərəcə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__medium_id
msgid "Medium"
msgstr "Reklam Vasitəsi"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_mediums
msgid "Mediums"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_date
msgid "Meeting Display Date"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_text
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_text
msgid "Meeting Display Text"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_ids
msgid "Meetings"
msgstr "Görüşlər"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_ir_ui_menu
msgid "Menu"
msgstr "Menyu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Mission Dates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "My Applications"
msgstr "Mənim Tətbiqlərim"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "My Candidates"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "My Favorites"
msgstr "Mənim Seçilmişlərim"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_search_view
msgid "My Job Positions"
msgstr "Mənim işdəki mövqeyim"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Name"
msgstr "Ad"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "Yeni"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_new
msgid "New Applicant"
msgstr "Yeni Namizəd"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr "Yeni Namizədlər"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__new_application_count
msgid "New Application"
msgstr "Yeni Müraciət"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "New Applications"
msgstr "Yeni Müraciətlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
msgid "New Employees"
msgstr "Yeni İşçilər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr "İşə Qəbul Edilən Yeni İşçi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr "Növbəti Fəaliyyətlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Next Meeting"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "No Meeting"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "No application found. Let's create one !"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr "Hal-hazırda müraciət yoxdur"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_candidate
msgid "No candidates yet"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid "No data to display"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_analysis
msgid "No data yet!"
msgstr "Hələ məlumat yoxdur!"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "No rules have been defined."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__0
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__0
msgid "Normal"
msgstr "Normal"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_normalized
msgid "Normalized Email"
msgstr "Normallaşdırılmış Email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Note"
msgstr "Qeyd"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_count
msgid "Number of Attachments"
msgstr "Qoşmaların Sayı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__new_application_count
msgid ""
"Number of applications that are new in the flow (typically at first step of "
"the flow)"
msgstr ""
"Siyahıya yeni daxil edilən tətbiqlərin sayı (adətən siyahının ilk addımında)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr "Bağlanacaq günlərin sayı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo, işə qəbul prosesində müraciət edənləri izləməyinizə\n"
"                və görüşlər, müsahibələr və s. kimi bütün əməliyyatlara nəzarət etməyinizə kömək edir."

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer: Manage all applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__old_application_count
msgid "Old Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__ongoing
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr "Davamlı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr "Onlayn Göndərmə"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Operation not supported"
msgstr "Əməliyyat dəstəklənmir"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Cavab verməsələr belə, gələn bütün mesajların əlavə ediləcəyi email "
"zəncirinin (qeyd) istəyə bağlı ID-si. Quraşdırılarsa, bu, yeni qeydlərin "
"yaradılmasını tamamilə dayandıracaq."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,help:hr_recruitment.field_candidate_send_mail__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Or talk about this applicant privately with your colleagues."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Other Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__other_applications_count
msgid "Other Applications Count"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other applications"
msgstr "Digər Tətbiqlər "

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other benefits"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "Əsas Model"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Əsas Qeyd Zəncirinin ID-si"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Qoşma adı özündə saxlayan əsas model Qoşma ad istinadını özündə saxlayan "
"model  alias_model_id tərəfindən verilən model olmaya bilər (məsələn: layihə"
" (parent_model) və tapşırıq (model))"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Partner Name"
msgstr "Tərəfdaşın Adı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone
msgid "Phone"
msgstr "Telefon"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Telefon Qarasiyahısı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Telefon/Mobil "

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Please provide an candidate name."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mail şlüzündən istifadə edərək sənəddəki mesajı göndərmək qaydası.\n"
"- hər kəs: hərkəs göndərə bilər\n"
"- tərəfdaşlar: yalnız təsdiqlənmiş tərəfdaşlar\n"
"- izləyicilər: yalnız müvafiq sənədin izləyiciləri və ya aşağıdakı kanalların üzvləri\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "Ehtimal"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Process"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__candidate_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__job_properties
msgid "Properties"
msgstr "Xüsusiyyətlər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr "Təklif olunan Əlavə Əmək haqqı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Provide an email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish job offers on your website"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__rating_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Ready for Next Stage"
msgstr "Növbəti Mərhələyə Hazır"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr "Daha səmərəli seçimə hazırsınız?"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Reason"
msgstr "Səbəb"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Qeyd Hissəciyinin ID-si"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruiter"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "Seçim"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr "İşçilərin/namizədlərin seçim mərhələləri"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr "Seçim təhlili"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Recruitment Plans"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Seçim mərhələləri"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_congratulations
msgid "Recruitment: Application Acknowledgement"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_interest
msgid "Recruitment: Interest"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_not_interested
msgid "Recruitment: Not interested anymore"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_refuse
msgid "Recruitment: Refuse"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__blocked
msgid "Red"
msgstr "Qırmızı"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Qırmızı Kanban Leyblı"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.ir_actions_server_refuse_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refuse"
msgstr "İmtina"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_date
msgid "Refuse Date"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.actions.act_window,name:hr_recruitment.applicant_get_refuse_reason_action
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__refuse_reason_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_reason_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Refuse Reason"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_refuse_reason
msgid "Refuse Reason of Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_refuse_reason_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_applicant_refuse_reason
msgid "Refuse Reasons"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Refuse the"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refused"
msgstr "İmtina edilib"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"Refused automatically because this application has been identified as a "
"duplicate of %(link)s"
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_2
msgid "Refused by applicant: job fit"
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_8
msgid "Refused by applicant: salary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__regex
msgid "Regex"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Remote"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__render_model
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__render_model
msgid "Rendering Model"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "Tələblər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Responsible"
msgstr "Məsul"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Restore"
msgstr "Bərpa edin"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Resume's content"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Running Applicants"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Digitization (OCR)"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Display"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr "Ərizəçi tərəfindən Gözlənilən Əmək haqqı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr "Ərizəçi tərəfindən Gözlənilən Əmək haqqı, əlavə üstünlüklər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Salary Package"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr "Təşkilat tərəfindən Təklif olunan Əmək haqqı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr ""
"Əmək haqqı qaydasıTəşkilat tərəfindən Təklif olunan Əmək haqqı, əlavə "
"üstünlüklər"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized
msgid "Sanitized Number"
msgstr " Filtlənmiş Nömrə"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone_sanitized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone_sanitized
msgid "Sanitized Phone Number"
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Save it!"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr "Müsahibəni planlaşdırın"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Search Applicants"
msgstr "Tətbiqetmələrin Axtarışı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_view_search
msgid "Search Source"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr "İkinci Müsahibə"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid ""
"Select the location where the applicant will work. Addresses listed here are"
" defined on the company's contact information."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Send"
msgstr "Göndərin"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_extract
msgid "Send CV to OCR to fill applications"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.server,name:hr_recruitment.action_applicant_send_mail
#: model:ir.actions.server,name:hr_recruitment.action_candidate_send_mail
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__send_mail
msgid "Send Email"
msgstr "Emaili göndər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send Interview Survey"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_applicant_mass_sms
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_candidate_mass_sms
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send SMS"
msgstr "SMS Göndər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Send an Interview Survey to the applicant during the recruitment process"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_send_mail
msgid "Send mails to applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_candidate_send_mail
msgid "Send mails to candidates"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "Kontaktlarınıza mətnlər göndərin"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Send your email. Followers will get a copy of the communication."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_interest
msgid ""
"Set this template to a recruitment stage to send it when applications reach "
"that stage"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "Parametrlər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Similar Candidates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Similar Candidates Count"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__single_applicant_email
msgid "Single Applicant Email"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Some values do not exist in the application status"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "Mənbə"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Müraciət Edənlərin Mənbəyi"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_sources
msgid "Sources"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr "Müraciət Edənlərin Mənbələri"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Sourcing"
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_7
msgid "Spam"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Specific jobs that use this stage. Other jobs will not use this stage."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage"
msgstr "Mərhələ"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr "Mərhələ Dəyişdirildi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr "Mərhələnin tərifi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage Name"
msgstr "Mərhələnin Adı"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr "Mərhələ Dəyişdirildi"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""
"Namizədin cari mərhələdən əvvəl yerləşdiyi mərhələ İtki hallarının təhlili "
"üçün istifadə edildi."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "Mərhələlər"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__duration_tracking
msgid "Status time"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__subject
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__subject
msgid "Subject"
msgstr "Mövzu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Tag Name"
msgstr "Etiket Adı"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_applicant_category_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Tags"
msgstr "Etiketlər"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_job_platform_email_uniq
msgid ""
"The Email must be unique, this one already corresponds to another Job "
"Platform."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__interviewer_ids
msgid ""
"The Interviewers set on the job position can see all Applicants in it. They "
"have access to the information, the attachments, the meeting management and "
"they can refuse him. You don't need to have Recruitment rights to be set as "
"an interviewer."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__user_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_campaign.py:0
msgid ""
"The UTM campaign '%s' cannot be deleted as it is used in the recruitment "
"process."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid ""
"The candidate is linked to an employee, to avoid losing information, archive"
" it instead."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__availability
msgid "The date at which the applicant will be available to start working"
msgstr "Ərizəçiyə işə başlamaq üçün münasib tarix"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_send_mail.py:0
msgid "The following applicants are missing an email address: %s."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/candidate_send_mail.py:0
msgid "The following candidates are missing an email address: %s."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Bu alternativ adın uyğun gəldiyi model (Odoo Document Kind). Mövcud qeydə "
"cavab verməyən hər hansı gələn email bu modelin yeni bir qeydinin "
"yaradılmasına səbəb olacaq (məsələn, Layihə Tapşırığı)"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_recruitment_degree_name_uniq
msgid "The name of the Degree of Recruitment must be unique!"
msgstr "İşə Qəbul Dərəcəsinin adı unikal olmalıdır!"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>> ünvanından olan mailləri görmək istəyirsinizsə, "
"email alternativininin (məs: işlər) adı"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__regex
msgid ""
"The regex facilitates to extract information from the subject or body of the"
" received email to autopopulate the Applicant's name field"
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"These job positions and all related applicants will be archived. Are you "
"sure?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid ""
"This displays how the applicant has reached out, e.g. via Email, LinkedIn, "
"Website, etc."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Əsas e-poçt sahəsində yalnız daha vacib e-poçt ünvanı ola bilər, çünki bu "
"sahədən, e-poçt ünvanı axtarışı üçün istifadə olunur."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Bu, Fall_Drive, Christmas_Special kimi müxtəlif kampaniya səylərini izləməyə"
" kömək edən bir addır"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Bu, axtarış mühərriki, digər domen və ya e-poçt siyahısı adı kimi istinad "
"mənbəyidir"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"This job position and all related applicants will be archived. Are you sure?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Bu mərhələdə göstərmək üçün heç bir qeyd olmadığı zaman bu mərhələ Kanban "
"görünüşündə dəyişir."

#. module: hr_recruitment
#: model:digest.tip,name:hr_recruitment.digest_tip_hr_recruitment_0
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Tip: Let candidates apply by email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "To Recruit"
msgstr "İşə Qəbul Etmək Üçün"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr "Çıxan izahlar"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Trackers"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Try creating an application by sending an email to"
msgstr ""

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Try sending an email"
msgstr "Elektron poçt göndərməyə cəhd edin"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Try the Website app to publish job offers online."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_campaign
msgid "UTM Campaign"
msgstr "Kampaniyanın UTM kodu"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm
msgid "UTMs"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Unarchive"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Unassigned"
msgstr "Təyin olunmamış"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unread Messages"
msgstr "Oxunmamış Mesajlar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use OCR to fill data from a picture of the Résumr or the file itself"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Use emails and links trackers"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on the Survey app."
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_users
msgid "User"
msgstr "İstifadəçi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr "İstifadəçinin Emaili"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__2
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__2
msgid "Very Good"
msgstr "Çox Yaxşı"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Waiting"
msgstr "Gözlənilir"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Want to analyse where applications come from ?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "What do you want to recruit today? Choose a job title..."
msgstr ""

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_refuse
msgid "When you refuse an application, you can choose this template"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Who can access candidates"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Without a regex: The applicant's name will be the email's subject."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Write your message here..."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You are not allowed to perform this action."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                            of the default labels."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define the requirements here. They will be displayed when you hover "
"over the stage title."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"You can't select Send email option.\n"
"The email will not be sent to the following applicant(s) as they don't have an email address:"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"You cannot create an applicant in a different company than the candidate"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following recruitment sources in Recruitment:\n"
"%(recruitment_sources)s"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for %s"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for the Applicant %s"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You must define a Contact Name for this applicant."
msgstr "Siz bu ərizəçi üçün Kontakt Adı müəyyən etməlisiniz."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You must define a Contact Name for this candidate."
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: {{ object.job_id.name }}"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.quick_create_applicant_form
msgid "e.g. John Doe"
msgstr "məs. Con Dou"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "e.g. LinkedIn"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. Linkedin"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. Masters"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr "məs. Satış Meneceri"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. ^New application:.*from (.*)"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. domain.com"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. https://www.linkedin.com/in/..."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. jobs"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. mycompany.com"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. sales-manager"
msgstr ""

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "or"
msgstr "və yaxud"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "other application(s)"
msgstr ""
