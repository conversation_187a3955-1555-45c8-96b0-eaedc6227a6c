# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_synchronization
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"\n"
"\n"
"If you've already opened a ticket for this issue, don't report it again: a support agent will contact you shortly."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_duplicate_transactions.py:0
msgid "%s duplicate transactions"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
").\n"
"                    This might cause duplicate entries."
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "0 transaction fetched"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "<i class=\"fa fa-trash me-1\"/> Delete Selected"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_form
msgid "<i class=\"oi oi-arrow-right me-2\"/> Send Now"
msgstr ""

#. module: account_online_synchronization
#: model:mail.template,body_html:account_online_synchronization.email_template_sync_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            The connection between <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b> and <t t-out=\"object.account_online_link_id.name or ''\">Belfius</t> <t t-if=\"not object.expiring_synchronization_due_day\">expired.</t><t t-else=\"\">expires in <t t-out=\"object.expiring_synchronization_due_day or ''\">10</t> days.</t><br/>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/renew_consent/{{ object.id }}?access_token={{object.access_token}}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Renew Consent\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to remind you before a bank sync consent expiration.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__access_token
msgid "Access Token"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_data
msgid "Account Data"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__name
msgid "Account Name"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__name
msgid "Account Name as provided by third party provider"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__account_number
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_number
msgid "Account Number"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_account_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__account_online_account_ids
msgid "Account Online Account"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_online_link_id
msgid "Account Online Link"
msgstr ""

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_waiting_synchronization_ir_actions_server
msgid "Account: Journal online Waiting Synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_ir_actions_server
msgid "Account: Journal online sync"
msgstr ""

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_unused_connection_cron_ir_actions_server
msgid "Account: Journal online sync cleanup unused connections"
msgstr ""

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_mail_cron_ir_actions_server
msgid "Account: Journal online sync reminder"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_ids
msgid "Activities"
msgstr "गतिविधियाँ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_state
msgid "Activity State"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount
msgid "Amount"
msgstr "रकम"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount_currency
msgid "Amount in Currency"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__auto_sync
msgid "Automatic synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__balance
msgid "Balance"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__balance
msgid "Balance of the account sent by the third party provider"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Bank"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_link
msgid "Bank Connection"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account_online_synchronization
#: model:mail.activity.type,name:account_online_synchronization.bank_sync_activity_update_consent
msgid "Bank Synchronization: Update consent"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Bank Synchronization: Update your consent"
msgstr ""

#. module: account_online_synchronization
#: model:mail.template,name:account_online_synchronization.email_template_sync_reminder
msgid "Bank connection expiration reminder"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Cancel"
msgstr "रद्द"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Check the documentation"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"Choose a date and a journal from which you want to check the transactions."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Choose a date and a journal from which you want to fetch transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__client_id
msgid "Client"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Client id"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__renewal_contact_email
msgid ""
"Comma separated list of email addresses to send consent renewal "
"notifications 15, 3 and 1 days before expiry"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_company
msgid "Companies"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__company_id
msgid "Company"
msgstr "संस्था"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Connect"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Connect Bank"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Connect bank"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect my Bank"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect your bank account to Odoo"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__connected
msgid "Connected"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Connected until"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__renewal_contact_email
msgid "Connection Requests"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__connection_state_details
msgid "Connection State Details"
msgstr ""

#. module: account_online_synchronization
#: model:mail.message.subtype,name:account_online_synchronization.bank_sync_consent_renewal
msgid "Consent Renewal"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_partner
msgid "Contact"
msgstr "संपर्क"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_uid
msgid "Created by"
msgstr "द्वारा निर्मित"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_date
msgid "Created on"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__currency_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__currency_id
msgid "Currency"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__date
msgid "Date"
msgstr "तिथि"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Date when the consent for this connection expires"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__display_name
msgid "Display Name"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__done
msgid "Done"
msgstr "हो गया"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__error
msgid "Error"
msgstr "त्रुटि!"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Expiring Synchronization Date"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_due_day
msgid "Expiring Synchronization Due Day"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Extend Connection"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_data
msgid "Extra information needed by third party provider"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch Missing Bank Statements Wizard"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Transactions"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Fetched Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__online_sync_fetching_status
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__fetching_status
msgid "Fetching Status"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Fetching..."
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/find_duplicate_transactions_cog_menu.xml:0
msgid "Find Duplicate Transactions"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/fetch_missing_transactions_cog_menu.xml:0
msgid "Find Missing Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__first_ids_in_group
msgid "First Ids In Group"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__foreign_currency_id
msgid "Foreign Currency"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_message
msgid "Has Message"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid "Has Unlinked Accounts"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Here"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__id
msgid "ID"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__online_identifier
msgid "Id used to identify account by third party provider"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "If checked, the balance sign will be inverted"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "If checked, the transaction sign will be inverted"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__auto_sync
msgid ""
"If possible, we will try to automatically fetch new transactions for this record\n"
"                \n"
"If the automatic sync is disabled. that will be due to security policy on the bank's end. So, they have to launch the sync manually"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "Import Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_data
msgid "Information needed to interact with third party provider"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__name
msgid "Institution Name"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Internal Error"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid URL"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid value for proxy_mode config parameter."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "Inverse Balance Sign"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "Inverse Transaction Sign"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_journal
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__journal_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__journal_ids
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Journal"
msgstr "पत्रिका"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"Journal %(journal_name)s has been set up with a different currency and "
"already has existing entries. You can't link selected bank account in "
"%(currency_name)s to it"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__last_refresh
msgid "Last Refresh"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Last refresh"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__last_sync
msgid "Last synchronization"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Latest Balance"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_selection
msgid "Link a bank account to the selected journal"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Manual Bank Statement Lines"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Message"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_ids
msgid "Messages"
msgstr "संदेश"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Missing and Pending Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__name
msgid "Name"
msgstr "नाम"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__next_refresh
msgid "Next synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__disconnected
msgid "Not Connected"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Odoo"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_account_id
msgid "Online Account"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Online Accounts"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__online_identifier
msgid "Online Identifier"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__next_link_synchronization
msgid "Online Link Next synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_partner__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_users__online_partner_information
msgid "Online Partner Information"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: model:ir.actions.act_window,name:account_online_synchronization.action_account_online_link_form
#: model:ir.ui.menu,name:account_online_synchronization.menu_action_online_link_account
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid "Online Synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_transaction_identifier
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_transaction_identifier
msgid "Online Transaction Identifier"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
msgid "Opening statement: first synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__partner_name
msgid "Partner Name"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__payment_ref
msgid "Payment Ref"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__pending
msgid "Pending"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__planned
msgid "Planned"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Please enter a valid Starting Date to continue."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Please reconnect your online account."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "Please select first the transactions you wish to import."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__posted
msgid "Posted"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.missing_bank_statement_line_search
msgid "Posted Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__processing
msgid "Processing"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_data
msgid "Provider Data"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_type
msgid "Provider Type"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reconnect"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Reconnect Bank"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Redirect"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Refresh"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__refresh_token
msgid "Refresh Token"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "Report Issue"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Report issue"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__client_id
msgid "Represent a link for a given user towards a banking institution"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reset"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Search over"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid ""
"Security Tip: always check the domain name of this page, before clicking on "
"the button."
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "See error"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select a Bank Account"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select the"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__selected_account
msgid "Selected Account"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__sequence
msgid "Sequence"
msgstr "अनुक्रम"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_number
msgid "Set if third party provider has the full account number"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Setup Bank"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Setup Bank Account"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "Some transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__date
msgid "Starting Date"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__state
msgid "State"
msgstr "स्थिति"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Thank You!"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "The consent for the selected account has expired."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"The online synchronization service is not available at the moment. Please "
"try again later."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_type
msgid "Third Party Provider"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"This action will delete all selected transactions. Are you sure you want to "
"proceed?"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "This button will reset the fetching status"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"This version of Odoo appears to be outdated and does not support the '%s' "
"sync mode. Installing the latest update might solve this."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid ""
"To create a synchronization with your banking institution,<br>\n"
"                please click on <b>Add a Bank Account</b>."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__access_token
msgid "Token used to access API."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__refresh_token
msgid "Token used to sign API request, Never disclose it"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__transaction_ids
msgid "Transaction"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__transaction_details
msgid "Transaction Details"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "Transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line_transient
msgid "Transient model for bank statement line"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid ""
"True if that connection still has accounts that are not linked to an Odoo "
"journal"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Update Credentials"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__waiting
msgid "Waiting"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_duplicate_transaction_wizard
msgid "Wizard for duplicate transactions"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_missing_transaction_wizard
msgid "Wizard for missing transactions"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"You are importing transactions before the creation of your online synchronization\n"
"                    ("
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You can contact Odoo support"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "You can only execute this action for bank-synchronized journals."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid ""
"You can't find missing transactions for a journal that isn't connected."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You cannot have two journals associated with the same Online Account."
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "You cannot import pending transactions."
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "You have"
msgstr ""

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "You have to select one journal to continue."
msgstr ""

#. module: account_online_synchronization
#: model:mail.template,subject:account_online_synchronization.email_template_sync_reminder
msgid "Your bank connection is expiring soon"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "account to connect:"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "banks"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "entries"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "loading..."
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "may be duplicates."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "on"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_account
msgid "representation of an online bank account"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "transactions fetched"
msgstr ""

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"within this period that were not created using the online synchronization. "
"This might cause duplicate entries."
msgstr ""
