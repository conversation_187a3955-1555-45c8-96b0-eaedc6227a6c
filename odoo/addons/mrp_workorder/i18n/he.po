# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# MichaelHadar, 2024
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# NoaFarkash, 2024
# yael terner, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Hed <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Ha <PERSON>tem <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:13+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to delete this instruction"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to use this document as instruction"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "+ New Operator"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "< Back"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> הדפס פעולות ברקוד"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_operation_form_view
msgid "<span class=\"o_stat_text\">Instructions</span>"
msgstr "<span class=\"o_stat_text\">הוראות</span>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "<span invisible=\"employee_name\">Log In </span>"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>תאריך התחלה: </strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>תאריך סיום: </strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>תחנת עבודה: </strong>"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add By-product"
msgstr "הוסף מוצר נלווה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add Component"
msgstr "הוסף רכיב"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/employees_panel.xml:0
msgid "Add Operator"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Add Work Order"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add a Step"
msgstr "הוסף שלב"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid "Add log note"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production_additional_workorder
msgid "Additional Workorder"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid ""
"Additional instructions that can be created and visualised from both here "
"and the shop floor interface."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__all_employees_allowed
msgid "All Employees Allowed"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "All MO"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allow_producing_quantity_change
msgid "Allow Changes to Producing Quantity"
msgstr "אפשר שינויים בכמות הייצור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__allow_registration
msgid "Allow Registration"
msgstr "אפשר רישום"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "Allowed Employees"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Archived"
msgstr "בארכיון"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_assigned_ids
msgid "Assigned"
msgstr "משויך"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Availability"
msgstr "זמינות"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "דו\"ח סקירה כללית של עץ מוצר"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Back"
msgstr "חזור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Barcode Scanned"
msgstr "ברקוד נסרק"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_bom
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_id
msgid "Bill of Material"
msgstr "עץ מוצר"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Binary file"
msgstr "קובץ בינארי"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Block"
msgstr "חסום"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "BoM feedback %(step)s (%(production)s - %(operation)s)"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "BoM feedback (%(production)s - %(operation)s)"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_product_ids
msgid "Bom Product"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE"
msgstr "המשך"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Cancel"
msgstr "בטל"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_change_production_qty
msgid "Change Production Qty"
msgstr "שנה כמות הייצור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__check_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__quality_check_ids
msgid "Check"
msgstr "בדיקה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "שיקים"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_log_note_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Close"
msgstr "סגור"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Close Production"
msgstr ""

#. module: mrp_workorder
#: model:product.attribute,name:mrp_workorder.product_attribute_color_radio
msgid "Color"
msgstr "צבע"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__comment
msgid "Comment"
msgstr "תגובה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__company_id
msgid "Company"
msgstr "חברה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_ids
msgid "Component"
msgstr "רכיב"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_qty_to_do
msgid "Component Qty To Do"
msgstr "כמות רכיב לביצוע"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Configuration"
msgstr "תצורה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Confirm"
msgstr "אשר"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__connected_employee_ids
msgid "Connected Employee"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__consumption
msgid "Consumption"
msgstr "צריכה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Continue"
msgstr "המשך"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Continue Consumption"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Continue consumption"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__total_cost
msgid "Cost"
msgstr "עלות"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.js:0
msgid "Could not display the selected %s"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid "Create a new operation type"
msgstr "צור סוג פעולה חדש"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Creates a new serial/lot number"
msgstr "צור מספר סידורי /מספר אצווה חדש"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__currency_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__current_quality_check_id
msgid "Current Quality Check"
msgstr "בדיקת איכות נוכחית"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__step
msgid "Custom"
msgstr "מותאם"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__product_description_variants
msgid "Custom Description"
msgstr "תיאור"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Date"
msgstr "תאריך"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__date_start
msgid "Date Start"
msgstr "תאריך התחלה"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "מגדיר את סוג נקודת בקרת האיכות."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Delete a Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_deleted
msgid "Deleted in production"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Discard"
msgstr "בטל"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Display Log Note"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Document"
msgstr "מסמך"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__qty_done
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__qty_done
msgid "Done"
msgstr "בוצע"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__done_check_ids
msgid "Done Check"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: model:ir.model,name:mrp_workorder.model_hr_employee
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__employee_assigned_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__employee_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employee"
msgstr "עובד"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Employee Capacity"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_costs_hour
msgid "Employee Hourly Cost"
msgstr "עלות שעתית של עובד"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_name
msgid "Employee Name"
msgstr "שם העובד"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employees"
msgstr "עובדים"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__component_tracking
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "וודא מעקב אחר מוצר מנוהל מלאי במחסן שלך."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Error during log out!"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__duration_expected
msgid "Expected Duration"
msgstr "משך זמן צפוי"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fail"
msgstr "נכשל"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fill in worksheet"
msgstr "מלא גליון עבודה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Finished"
msgstr "סיים"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_lot_id
msgid "Finished Lot/Serial"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Lot/Serial Number"
msgstr "אצווה/ מספר סידורי מוכן"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__finished_product_check_ids
msgid "Finished Product Check"
msgstr "בדיקת מוצר מוגמר"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_product_sequence
msgid "Finished Product Sequence Number"
msgstr "מספר רצף של מוצר מוגמר"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Google Doc"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Google Slide Link"
msgstr "קישור Google Slide "

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_url
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_url
msgid "Google doc URL"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__has_operation_note
msgid "Has Description"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__id
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__id
msgid "ID"
msgstr "מזהה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_document
msgid "Image/PDF"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Improvement Suggestion"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "In Progress"
msgstr "בתהליך"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__move_line_id
msgid ""
"In case of Quality Check by Quantity, Move Line on which the Quality Check "
"applies"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Indicate after which step you would like to add this one"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__blocked_by_workorder_id
msgid "Insert after operation"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Install App"
msgstr "התקנת אפליקציה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Instruction"
msgstr "הוראה"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Instruction:"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_count
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Instructions"
msgstr "הוראות"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Instructions ("
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "מהלכים מלאי שעבורם עליך לסרוק מספר אצווה בהזמנת עבודה זו"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_tracking
msgid "Is Component Tracked"
msgstr "האם הרכיב תחת מעקב"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_unfinished_wo
msgid "Is Last Work Order To Process"
msgstr "האם הוראת עבודה אחרונה לביצוע"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_lot
msgid "Is Last lot"
msgstr "אצווה אחרונה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_started_wo
msgid "Is The first Work Order"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__is_workorder_step
msgid "Is Workorder Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_user_working
msgid "Is the Current User Working"
msgstr "המשתמש הנוכחי עובד"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Load Samples"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_form_log_note
msgid "Log Note"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__log_note
msgid "Log note"
msgstr "כתוב הערה פנימית"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged in!"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged out!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Lot"
msgstr "אצווה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__lot_id
msgid "Lot/Serial"
msgstr "מספר סידורי/אצווה"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "דו\"ח סקירה כללית של הוראת ייצור"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_tablet_timer
msgid "Manage Work Order timer on Shop Floor"
msgstr "נהל את הטיימר של הוראות העבודה ברצפת הייצור"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Manage your manufacturing orders from the shop floor app"
msgstr "נהל את הוראות הייצור שלך מאפליקציית רצפת ייצור"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_shop_floor
msgid "Manage your manufacturing orders from the shop floor display app"
msgstr "נהל את הוראות הייצור שלך מאפליקציית רצפת ייצור"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "הוראת ייצור"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Manufacturing Orders"
msgstr "הוראות ייצור"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done"
msgstr "סמן כבוצע"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done and Close MO"
msgstr "סמן כבוצע וסגור הוראת ייצור"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Mass Produce"
msgstr "ייצור המוני"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Measure:"
msgstr "מדד:"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Menu"
msgstr "תפריט"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Move to work center"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "תנועות למעקב"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Department"
msgstr "המחלקה שלי"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Team"
msgstr "הצוות שלי"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "My WO"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_filter_my_work_orders
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_search_my_work_orders
msgid "My Work Orders"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__note
msgid "New Instruction"
msgstr "הוראה חדשה"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Instruction suggested by %(user_name)s"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "New Step suggested by %(user_name)s"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Title suggested:"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Newly Hired"
msgstr "עובדים חדשים"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Next"
msgstr "הבא"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__next_check_id
msgid "Next Check"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Next Operation"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "No manufacturing steps defined yet!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid "No work orders to do!"
msgstr "אין הוראות עבודה לביצוע!"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid ""
"No workcenters are available, please create one first to add it to the shop "
"floor view"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Notes"
msgstr "הערות"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Number of employees needed to complete operation."
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Open Manufacturing Order"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Open Shop Floor"
msgstr "פתיחת רצפת ייצור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workorder_id
msgid "Operation"
msgstr "פעולה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "Operations"
msgstr "פעולות"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Operator"
msgstr "מפעיל"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_stock_picking_type_action
#: model:ir.actions.server,name:mrp_workorder.action_view_mrp_overview
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_dashboard
msgid "Overview"
msgstr "סקירה כללית"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "PAUSE"
msgstr "השהה"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "PDF file"
msgstr "קובץ PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Pass"
msgstr "עבר"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Password?"
msgstr "סיסמה?"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Pause"
msgstr "השהה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Pending"
msgstr "ממתין "

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג ליקוט"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__picture
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__picture
msgid "Picture"
msgstr "תמונה"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_production
msgid "Planning by Production"
msgstr "תכנון לפי ייצור"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_workcenter
msgid "Planning by Workcenter"
msgstr "תכנון לפי תחנות עבודה "

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a Lot/SN."
msgstr "נא הכנס אצווה /מספר סידורי"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a positive quantity."
msgstr "הזן כמות חיובית."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"Please set the quantity you are currently producing. It should be different "
"from zero."
msgstr "נא הגדר את הכמות המיוצרת כרגע. היא צריכה להיות שונה מאפס."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please upload a picture."
msgstr "נא העלה תמונה."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__previous_check_id
msgid "Previous Check"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Print Labels"
msgstr "הדפס תוויות"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_print_label
msgid "Print label"
msgstr "הדפס תווית"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת שורת מלאי)"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_id
msgid "Product To Register"
msgstr "מוצר לרישום"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__production_id
msgid "Production"
msgstr "ייצור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__production_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__production_id
msgid "Production Order"
msgstr "הוראת ייצור"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Production Workcenter"
msgstr "תחנת עבודה לייצור"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__product_ids
msgid "Products"
msgstr "מוצרים"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Propose Change"
msgstr "הצע שינוי"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_propose_change
msgid "Propose a change in the production"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_alert
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_ids
msgid "Quality Alert"
msgstr "התראת איכות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_count
msgid "Quality Alert Count"
msgstr "כמות התראות איכות"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "בדיקת איכות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_fail
msgid "Quality Check Fail"
msgstr "בדיקת איכות נכשלה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_todo
msgid "Quality Check Todo"
msgstr "בדיקת איכות לביצוע"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "נקודת בקרת איכות"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "סוג בדיקת בקרת איכות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_ids
msgid "Quality Point"
msgstr "נקודת איכות"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Quality Point Steps"
msgstr "שלבי נקודת איכות"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "נקודת אבטחת איכות תחול על כל מוצר שנבחר."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_state
msgid "Quality State"
msgstr "מצב איכות"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Quantity"
msgstr "כמות"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
msgid "Quantity Produced"
msgstr "כמות מיוצרת"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Ready"
msgstr "מוכן"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Reason:"
msgstr "סיבה:"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Record production"
msgstr "רשום ייצור"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register %s"
msgstr ""

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_byproducts
msgid "Register By-products"
msgstr ""

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_consumed_materials
msgid "Register Consumed Materials"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_production
msgid "Register Production"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register Production: %s"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__additional
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__additional
msgid "Register additional product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_active
msgid "Related Bill of Material Active"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_remaining_qty
msgid "Remaining Quantity for Component"
msgstr "כמות שנותרה לרכיב"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__remove_step
msgid "Remove Current Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_report_type
msgid "Report Type"
msgstr "סוג דוח"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__result
msgid "Result"
msgstr "תוצאה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Scrap"
msgstr "פסול"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_open_employee_list
msgid "Select Employee"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.js:0
msgid "Select Work Centers for this station"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select a new work center"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select the step you want to modify"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Serial"
msgstr "מספר סידורי"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__set_picture
msgid "Set Picture"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Set a New picture"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.action_mrp_display
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_shop_floor
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_root
#: model:ir.ui.menu,name:mrp_workorder.menu_shop_floor
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Shop Floor"
msgstr "רצפת ייצור"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Shop Floor Control Panel"
msgstr "לוח בקרה רצפת ייצור"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_routing_workcenter_tree_view_inherited
msgid "Show Instructions"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Show the timer on the work order screen"
msgstr "הצג את הטיימר במסך הוראת העבודה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Skip >"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Slides viewer"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool
msgid "Small wooden stool"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__operation
msgid "Specific Page of Operation Worksheet"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__operation_id
msgid "Step"
msgstr "צעד מספרי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__source_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__source_document
msgid "Step Document"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__step_id
msgid "Step to change"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_mrp_workorder_show_steps
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_count
msgid "Steps"
msgstr "שלבים"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_id
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr "תנועת שורת מלאי"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool
msgid "Stool"
msgstr ""

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_foot
msgid "Stool Foot"
msgstr ""

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_top
msgid "Stool Top"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.tablet_client_action
msgid "Tablet Client Action"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type
msgid "Technical name"
msgstr "שם טכני"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_type_id
msgid "Test Type"
msgstr "סוג בדיקה"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "There is no session chief. Please log in."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_search_inherit_planning
msgid "This Station"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"This workcenter isn't expected to have open workorders during this period. "
"Work hours :"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "מעקב זמן: %(user)s"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_tablet_timer
msgid "Timer"
msgstr "טיימר"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__title
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__title
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Title"
msgstr "תואר"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Title:"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "To Process"
msgstr "לביצוע"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Total Qty"
msgstr "כמות כוללת"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid ""
"Track work orders, show instructions and record manufacturing operations from here:\n"
"                                    quality control, consumed quantities, lot/serial numbers, etc."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__product_tracking
msgid "Tracking"
msgstr "מעקב"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_workorder_form
msgid "Type"
msgstr "סוג"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__change_type
msgid "Type of Change"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid ""
"Unable to load samples when you already have existing manufacturing orders"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Unblock"
msgstr "בטל חסימה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Undo"
msgstr "שחזר"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Unit"
msgstr "יחידה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Units"
msgstr "יחידה"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.production_order_unplan_server_action
msgid "Unplan orders"
msgstr "בטל תכנון הוראות"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_uom_id
msgid "UoM"
msgstr "יחידת מידה"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__update_step
msgid "Update Current Step"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Update Instructions"
msgstr "עדכן הוראות"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Upload your PDF file."
msgstr "העלה את קובץ ה- PDF שלך."

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid ""
"Use steps to show instructions on a worksheet to operators, or trigger "
"quality checks at specific steps of the work order."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Use the table work center control panel to register operations in the shop floor directly.\n"
"            The tablet provides worksheets for your workers and allow them to scrap products, track time,\n"
"            launch a maintenance request, perform quality tests, etc."
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Validate"
msgstr "אשר"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "הערך של הברקוד האחרון שנסרק."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_bar.xml:0
msgid "WO"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "WO Filters"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Waiting"
msgstr "ממתין"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "What do you want to do?"
msgstr "מה אתה רוצה לעשות?"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Will be placed at the beginning if emtpy"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_alert_view_search_inherit_mrp_workorder
msgid "Work Center"
msgstr "תחנת עבודה"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "שימוש בתחנת עבודה"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "הוראת עבודה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_tree
msgid "Work Order Operation"
msgstr "פעולת הוראת עבודה"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_workorder_action_tablet
#: model:ir.ui.menu,name:mrp_workorder.mrp_workorder_menu_planning
msgid "Work Orders"
msgstr "הוראות עבודה"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_production
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_workcenter
msgid "Work Orders Planning"
msgstr "תכנון הוראות עבודה"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_view_kanban_inherit_workorder
msgid "Work orders"
msgstr "הוראות עבודה"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Workcenter Control Panel"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "יומן יצרנות של תחנת העבודה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__working_state
msgid "Workcenter Status"
msgstr "סטטוס תחנת עבודה "

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_ids
msgid "Working employees"
msgstr "עובדים פעילים"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__workorder_id
msgid "Workorder"
msgstr "הוראת עבודה"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_worksheet.xml:0
msgid "Worksheet"
msgstr "גיליון עבודה"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_page
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_page
msgid "Worksheet Page"
msgstr "דף גיליון"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__worksheet_page
msgid "Worksheet page"
msgstr "דף גיליון"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Wrong password!"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allow to work on some of these work orders."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allowed to work on the workorder"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/change_production_qty.py:0
msgid ""
"You cannot update the quantity to do of an ongoing manufacturing order for "
"which quality checks have been performed."
msgstr ""
"אינך יכול לעדכן את הכמות לביצוע בהוראת ייצור שמתבצעת ושבוצעו לה בדיקות "
"איכות."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "You did not set a lot/serial number for the final product"
msgstr "לא הגדרת מספר סידורי /אצווה עבור המוצר הסופי"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You must be logged in to process some of these work orders."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to complete Quality Checks using the Shop Floor before marking Work"
" Order as Done."
msgstr "עליך להשלים בדיקות איכות ברצפת הייצור לפני ביצוע הוראת עבודה."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productive'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to link this user to an employee of this company to process the "
"work order"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You need to log in to process this work order."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You still need to do the quality checks!"
msgstr "אתה עדיין צריך לבצע את בדיקות האיכות!"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Your suggestion to delete the %s step was succesfully created."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "back"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter_productivity__employee_id
msgid "employee that record this working time"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_cost
msgid "employee_cost"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "employees with access"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "if left empty, all employees can log in to the workcenter"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "menu"
msgstr "תפריט"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "per employee"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_foot
msgid "wooden stool foot"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_top
msgid "wooden stool top"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__employee_ids
msgid "working employees"
msgstr "עובדים פעילים"
