<?xml version="1.0" encoding="utf-8"?>
<odoo>

<record id="event_event_view_form_add" model="ir.ui.view">
    <field name="name">event.event.view.form.add</field>
    <field name="model">event.event</field>
    <field name="arch" type="xml">
        <form js_class="website_new_content_form">
            <group>
                <field name="website_url" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <field name="name" placeholder="e.g. &quot;Conference for Architects&quot;" string="Event Name"/>
                <field name="address_id" context="{'show_address': 1}"/>
                <field name="date_begin" string="Start &#8594; End" widget="daterange" options="{'end_date_field': 'date_end'}" />
                <field name="date_end" invisible="1" />
            </group>
        </form>
    </field>
</record>

<record id="event_event_action_add" model="ir.actions.act_window">
    <field name="name">New Event</field>
    <field name="res_model">event.event</field>
    <field name="view_mode">form</field>
    <field name="target">new</field>
    <field name="view_id" ref="event_event_view_form_add"/>
    <field name="context">{'default_address_id': False}</field>
</record>

</odoo>
