# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
#
# Translators:
# <PERSON><PERSON><PERSON> <iam<PERSON><EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2023
# fardin mardani, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: fardin mardani, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "#.ثبت نام‌ها"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
")\n"
"                                <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "<em>Write here a quote from one of your attendees. It gives confidence in your events.</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">Introduction</font>"
msgstr "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">مقدمه</font>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" title=\"Facebook\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" title=\"Instagram\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" title=\"LinkedIn\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left me-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" title=\"Youtube\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "<span class=\"badge text-bg-secondary text-uppercase o_wevent_badge\">Speaker</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 me-auto pe-sm-4\">Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span class=\"o_countdown_metric pe-1\">days</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_hours\">00</span><span class=\"o_countdown_metric\">:</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span class=\"o_countdown_metric\">:</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span class=\"o_countdown_metric\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-start\">Tickets</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark fw-bold align-middle px-2\">Qty</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "درباره ما"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "All Countries"
msgstr "تمام کشورها"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "All Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__name
msgid "Answer"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Answers"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "At just 13 years old, John DOE was already starting to develop his first business applications for customers. After mastering civil engineering, he founded TinyERP. This was the first phase of OpenERP which would later became Odoo, the most installed open-source business software worldwide."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "شرکت‌کنندگان"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "مولف"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Business Workshops"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "می تواند منتشر کند"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "لغو"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Cards"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "بستن"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Community"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__company_name
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "شرکت"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Conference For Architects"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "کشورها"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "خصوصیات رویه"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "تاریخ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "تاریخ و زمان"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "توصیف"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Don't forget to click <b>save</b> when you're done."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__email
msgid "Email"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__event_id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "رویداد"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "نام رویداد"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_question
msgid "Event Question"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_question_answer
msgid "Event Question Answer"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "ثبت نام رویداد"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_type_id
msgid "Event Type"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.snippets
msgid "Events"
msgstr "رویدادها"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "منقضی شده"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Find out what people see and say about this event, and join the conversation."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Fold Tickets Details"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__general_question_ids
msgid "General Questions"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "گوگل"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Great Reno Ballon Race"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "شبکه"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Hockey Tournament"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__id
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "شناسه"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_question__once_per_order
msgid "If True, this question will be asked only once and its value will be propagated to every attendees.If not it will be asked for every attendee of a reservation."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
msgid "Introduction"
msgstr "مقدمه"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "منتشر شده است"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "طرح بندی"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "فهرست"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Live Music Festival"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Location"
msgstr "مکان"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Looking great! Let's now <b>publish</b> this page so that it becomes <b>visible</b> on your website!"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Mandatory"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "منو"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "منوها"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__name
msgid "Name"
msgstr "نام"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "رویداد جدید"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
msgid "Next Events"
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "No Answers yet!"
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Once per Order"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "OpenWood Collection Online Reveal"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "سازمان‌دهنده"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Past Events"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__phone
msgid "Phone"
msgstr "تلفن"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
msgid "Please select at least one ticket."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "منتشر شده"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Question"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "Question cannot belong to both the event category and itself."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Questions"
msgstr "پرسش ها"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr ""

#. module: website_event
#. odoo-python
#. odoo-javascript
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register"
msgstr "ثبت نام"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration"
msgstr "ثبت"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "محدود کردن انتشار به این وبسایت."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "بهینه سازی شده سئو"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "نمونه"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "نام سئو"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__sequence
msgid "Sequence"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "تمام شد"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Stats"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Templates"
msgstr "قالبها"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "آدرس URL کامل برای دسترسی سند در وبسایت."

#. module: website_event
#: model:ir.model.constraint,message:website_event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "This month"
msgstr "این ماه"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "This shortcut will bring you right back to the event form."
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "This ticket is not available for sale for this event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__title
msgid "Title"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Today"
msgstr "امروز"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
msgid "Type"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Upcoming Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "نما"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "قابل دید در وبسایت حاضر"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "بازدید کننده"

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "تارنما"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr ""

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "منوی وبسایت"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "فیلتر قطعه وب سایت"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "URL وبسایت"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "بازدید کننده وبسایت"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "توضیحات متا وبسایت"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "کلیدواژه‌های متا وبسایت"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "عنوان متا وبسایت"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "تصویر opengraph وب سایت"

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "With the Edit button, you can <b>customize</b> the web page visitors will see when registering."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "You cannot change the question type of a question that already has answers!"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "You cannot delete a question that has already been answered by attendees."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question_answer.py:0
msgid "You cannot delete an answer that has already been selected by attendees."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr ""
