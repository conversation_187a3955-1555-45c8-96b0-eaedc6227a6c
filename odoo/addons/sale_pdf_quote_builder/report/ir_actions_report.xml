<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_report_saleorder_raw" model="ir.actions.report">
        <field name="name">Quotation / Order</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale.report_saleorder_raw</field>
        <field name="report_file">sale.report_saleorder_raw</field>
        <field name="print_report_name">(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or 'Order - %s' % (object.name)</field>
        <field name="binding_model_id" ref="sale.model_sale_order"/>
        <field name="binding_type">report</field>
    </record>

    <record id="sale.action_report_saleorder" model="ir.actions.report">
        <field name="name">PDF Quote</field>
    </record>

</odoo>
