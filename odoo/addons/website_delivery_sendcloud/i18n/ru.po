# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_delivery_sendcloud
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_delivery_sendcloud
#: model:ir.model.fields,help:website_delivery_sendcloud.field_delivery_carrier__sendcloud_use_locations
msgid ""
"Allows the ecommerce user to choose a pick-up point as delivery address."
msgstr ""
"Позволяет пользователю электронной коммерции выбрать пункт самовывоза в "
"качестве адреса доставки."

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_locations_id
msgid "Locations Id"
msgstr "Местонахождение Id"

#. module: website_delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:website_delivery_sendcloud.view_delivery_carrier_form
msgid "Locations Radius"
msgstr "Радиус расположения"

#. module: website_delivery_sendcloud
#: model:ir.model.fields,help:website_delivery_sendcloud.field_delivery_carrier__sendcloud_locations_radius_value
msgid "Maximum locations distance radius."
msgstr "Радиус максимального расстояния до места."

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_can_customize_use_locations
msgid "Sendcloud Can Customize Use Locations"
msgstr "Sendcloud может настраивать местоположение использования"

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_locations_radius_value
msgid "Sendcloud Locations Radius"
msgstr ""

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_locations_radius_unit
msgid "Sendcloud Locations Radius Unit"
msgstr ""

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_locations_radius_unit_name
msgid "Sendcloud Radius Unit Name"
msgstr ""

#. module: website_delivery_sendcloud
#: model:ir.model,name:website_delivery_sendcloud.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: website_delivery_sendcloud
#. odoo-python
#: code:addons/website_delivery_sendcloud/models/delivery_sendcloud.py:0
msgid "The maximum radius allowed is %(distance)d%(unit)s"
msgstr ""

#. module: website_delivery_sendcloud
#. odoo-python
#: code:addons/website_delivery_sendcloud/models/delivery_sendcloud.py:0
msgid "The minimum radius allowed is %(distance)d%(unit)s"
msgstr ""

#. module: website_delivery_sendcloud
#: model:ir.model.fields,field_description:website_delivery_sendcloud.field_delivery_carrier__sendcloud_use_locations
msgid "Use Sendcloud Locations"
msgstr "Использование местоположений Sendcloud"
