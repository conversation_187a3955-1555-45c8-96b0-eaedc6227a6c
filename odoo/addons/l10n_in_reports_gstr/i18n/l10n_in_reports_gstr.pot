# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_reports_gstr
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-31 13:19+0000\n"
"PO-Revision-Date: 2024-07-31 13:19+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid ""
"1. Send GSTR-1 \n"
"                        <small class=\"text-muted\">Details of customers invoices</small>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid ""
"2. Receive GSTR-2B\n"
"                        <small class=\"text-muted\">Details submited by vendors</small>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "3. GSTR-3"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Being Processed</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Error in Invoice</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Matched</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Not Recived</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Partially Matched</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Sending</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Sent</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">To Send</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\" style=\"font-size: "
"11px;\">Waiting for Status</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-muted\" style=\"font-size: "
"11px;\">Not Recived</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-muted\" style=\"font-size: "
"11px;\">To Send</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-success\" style=\"font-size: "
"11px;\">Being Processed</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-success\" style=\"font-size: "
"11px;\">Filed</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-success\" style=\"font-size: "
"11px;\">Matched</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-success\" style=\"font-size: "
"11px;\">Sending</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-success\" style=\"font-size: "
"11px;\">Sent</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Being Processed</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Error in Invoice</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Matched</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Not Recived</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Partially Matched</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Sending</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Sent</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">To Send</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid ""
"<span class=\"badge rounded-pill text-bg-warning\" style=\"font-size: "
"11px;\">Waiting for Status</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_get_otp_validate_wizard
msgid ""
"<span>OTP Sent on mobile on registered mobile number with company GST "
"number</span>"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/res_config_settings.py:0
#, python-format
msgid "API credentials validated successfully"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_ids
msgid "Activities"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_state
msgid "Activity State"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__04
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__quarter__04
msgid "April"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__08
msgid "August"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Before set as Filed, Status of GSTR-1 must be send"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_status__being_processed
msgid "Being Processed"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Bill Date as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Bill type as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__bills_not_in_gstr2
msgid "Bills Not in GSTR-2"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr1_blocking_level
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Return filing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Return filing operation."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Buy credits"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_get_otp_validate_wizard
msgid "Cancel"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Check Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Checkout our tutorial"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid ""
"Click on Send OTP to create token and verify username.\n"
"                            <br/>\n"
"                            <small>\n"
"                                *If you send OTP continuously more than 3 times your username will be blocked for 6 hours\n"
"                            </small>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model,name:l10n_in_reports_gstr.model_res_company
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__company_ids
msgid "Companies"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__company_id
msgid "Company"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model,name:l10n_in_reports_gstr.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid ""
"Costs 1 credit per transaction. Free 200 credits will be available for the "
"first time."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__create_date
msgid "Created on"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_account_move_form_inherit_account
msgid ""
"Credit notes for invoices from the financial year cannot be included in GSTR after November 30th.\n"
"                    Hence, it is advisable to remove the tax from the credit note."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__invoice_amount
msgid "Customer Invoices"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__12
msgid "December"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_bank_statement_line__l10n_in_reversed_entry_warning
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_move__l10n_in_reversed_entry_warning
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_payment__l10n_in_reversed_entry_warning
msgid "Display reversed entry warning"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Done"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_production_env
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_production_env
msgid "Enable the use of production credentials"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__end_date
msgid "End Date"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_blocking_level__error
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_blocking_level__error
msgid "Error"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__error_in_invoice
msgid "Error in Invoice"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr1_error
msgid "Error of GSTR-1"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_error
msgid "Error of GSTR-2B"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Error,"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_bank_statement_line__l10n_in_exception
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_move__l10n_in_exception
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_payment__l10n_in_exception
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__exception
msgid "Exception"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__expected_amount
msgid "Expected Amount"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__02
msgid "February"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Fetch GSTR-2B Summary"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__filed
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr3b_status__filed
msgid "Filed"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "First setup GST user name and validate using OTP from configuration"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__matched
msgid "Fully Matched"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_production_env
msgid "GST (IN) Is production environment"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_auto_refresh_token
msgid "GST (IN) Token Auto Refresh"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "GST Filing"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.actions.act_window,help:l10n_in_reports_gstr.l10n_in_gst_return_period_action
msgid "GST Return Filing"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#: model:ir.actions.act_window,name:l10n_in_reports_gstr.l10n_in_gst_return_period_action
#: model:ir.model,name:l10n_in_reports_gstr.model_l10n_in_gst_return_period
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_bank_statement_line__l10n_in_gst_return_period_id
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_move__l10n_in_gst_return_period_id
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_payment__l10n_in_gst_return_period_id
#, python-format
msgid "GST Return Period"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.ui.menu,name:l10n_in_reports_gstr.gst_return_period_menu
msgid "GST Return Periods"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_token
msgid "GST Token"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_token
msgid "GST Token (IN)"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_token_validity
msgid "GST Token (IN) Valid Until"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "GST Unit main company is different than this period company."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__tax_unit_id
msgid "GST Units"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_company__l10n_in_gstr_gst_username
msgid "GST User Name (IN)"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "GST Username"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_username
msgid "GST username"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid "GSTR-1"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr1_base_value
msgid "GSTR-1 Base Value"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "GSTR-1 Errors"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "GSTR-1 Processed with Error:<b>%s</b>"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "GSTR-1 Report"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "GSTR-1 Send data"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr_reference
msgid "GSTR-1 Submit Reference"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "GSTR-1 Successfully Sent"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "GSTR-1:"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__gstr2_bills_not_in_odoo
msgid "GSTR-2 Bills not in Odoo"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_kanban_view
msgid "GSTR-2B"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_base_value
msgid "GSTR-2B Base Value"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_bank_statement_line__l10n_in_gstr2b_reconciliation_status
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_move__l10n_in_gstr2b_reconciliation_status
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_account_payment__l10n_in_gstr2b_reconciliation_status
#, python-format
msgid "GSTR-2B Reconciliation"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "GSTR-2B:"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "GSTR-3 Report"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr3b_status
msgid "GSTR-3B Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_json_from_portal_ids
msgid "GSTR2B JSON from portal"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_account_invoice_filter
msgid "GSTR2B status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_get_otp_validate_wizard
msgid "Get Otp"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr1_blocking_level
msgid "Gstr1 Blocking Level"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr1_status
msgid "Gstr1 Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_blocking_level
msgid "Gstr2B Blocking Level"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr3b_closing_entry
msgid "Gstr3B Closing Entry"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__has_message
msgid "Has Message"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__id
msgid "ID"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_error
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "In case of issues"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "In case of issues re-fetch or re-match"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.actions.server,name:l10n_in_reports_gstr.ir_cron_auto_refresh_gst_token_ir_actions_server
msgid "Indian Accounting: Refresh GST Token"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.actions.server,name:l10n_in_reports_gstr.ir_cron_to_check_gstr1_status_ir_actions_server
msgid "Indian Accounting: To Check Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.actions.server,name:l10n_in_reports_gstr.ir_cron_to_send_gstr1_data_ir_actions_server
msgid "Indian Accounting: To Send Json"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.actions.server,name:l10n_in_reports_gstr.ir_cron_auto_sync_gstr2b_data_ir_actions_server
msgid "Indian Accounting; Download GST-2 Data"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.actions.server,name:l10n_in_reports_gstr.ir_cron_auto_match_gstr2b_data_ir_actions_server
msgid "Indian Accounting; Match GST-2 Data"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Indian GST"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_production_env
msgid "Indian GST Testing Environment"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/res_config_settings.py:0
#, python-format
msgid "Invalid OTP"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_auto_refresh_token
msgid "Is auto refresh token"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__01
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__quarter__01
msgid "January"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model,name:l10n_in_reports_gstr.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Json file that send to Government is attached here"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__07
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__quarter__07
msgid "July"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__06
msgid "June"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__03
msgid "March"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Mark as Filed"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Match Vendor Bills"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_status__fully_matched
msgid "Matched"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__05
msgid "May"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__company_ids
msgid "Members of this unit"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__month
msgid "Month"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__periodicity__monthly
msgid "Monthly"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Need help?"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr3b_status__not_filed
msgid "Not Filed"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_status__not_recived
msgid "Not Recived"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__11
msgid "November"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_res_config_settings__l10n_in_gstr_gst_otp
msgid "OTP"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__10
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__quarter__10
msgid "October"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/res_config_settings.py:0
#, python-format
msgid "Otp Request"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__partially_matched
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_status__partially_matched
msgid "Partially Matched"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__account_move__l10n_in_gstr2b_reconciliation_status__pending
msgid "Pending"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__name
msgid "Period"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__periodicity
msgid "Periodicity"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Please set company GSTIN"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Production Environment"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Push to GSTN"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__quarter
msgid "Quarter"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__periodicity__trimester
msgid "Quarterly"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Re-Push to GSTN"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_search
msgid "Recent Period"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Reconciled Bill"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Referance number as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Referance number is same other bills: %s"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Refresh Token"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__return_period_month_year
msgid "Return Period"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_tree_view
msgid "Return Periods"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.constraint,message:l10n_in_reports_gstr.constraint_l10n_in_gst_return_period_unique_period
msgid "Return period must be unique."
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Send OTP"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__sending
msgid "Sending"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__sent
msgid "Sent"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__month__09
msgid "September"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid "Setup GST Service for this company"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__start_date
msgid "Start Date"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__gstr2b_status
msgid "Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.actions.act_window,help:l10n_in_reports_gstr.l10n_in_gst_return_period_action
msgid ""
"Step-1: Send GSTR-1\n"
"                        1.Varify the GSTR-1 Report\n"
"                        2.Then Push to GSTN\n"
"                        3.Mark as Filed\n"
"\n"
"                    Step-2: Receive GSTR-2B\n"
"                        1.Fetch GSTR-2B Summary  \n"
"                        2.Match Vendor Bill\n"
"\n"
"                    Step-3: Submit GSTR-3\n"
"                        1.Varify the GSTR-3 Report\n"
"                        2.Validate GSTR-3 in GST Portal\n"
"                        3.Update &amp; Post closing entry"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "TO check status please push the GSTN"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid ""
"The NIC portal connection has expired. To re-initiate the connection, you "
"can send an OTP request From configuration."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Then,"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Then, Push to GSTN and"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid ""
"This Bill is Created from GSTR2B Reconciliation because no bill matched with"
" given details"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__to_send
msgid "To Send"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Total Taxable amount as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Total amount as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid ""
"Unable to connect to the GST service.The web service may be temporary down. "
"Please try again in a moment."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Update &amp; Post Closing Entry:"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.view_get_otp_validate_wizard
msgid "Validate"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Validate GSTR-3 in GST Portal"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/gst_return_period.py:0
#, python-format
msgid "Vat number as per GSTR-2B is %s"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__bill_amount
msgid "Vendor Bills"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "Verify the"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "View Reconciled Bills"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_status__waiting_reception
msgid "Waiting Reception"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_status__waiting_for_status
msgid "Waiting for Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr1_blocking_level__warning
#: model:ir.model.fields.selection,name:l10n_in_reports_gstr.selection__l10n_in_gst_return_period__gstr2b_blocking_level__warning
msgid "Warning"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,help:l10n_in_reports_gstr.field_l10n_in_gst_return_period__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: l10n_in_reports_gstr
#: model:ir.model.fields,field_description:l10n_in_reports_gstr.field_l10n_in_gst_return_period__year
msgid "Year"
msgstr ""

#. module: l10n_in_reports_gstr
#. odoo-python
#: code:addons/l10n_in_reports_gstr/models/res_config_settings.py:0
#, python-format
msgid "You must enable production environment to buy credits"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "and Check Status"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.res_config_settings_view_form_inherit_l10n_in_gstr
msgid ""
"if OTP is verified then the token will expire in 6 hours if you activate "
"this it will refresh the token before expiring until the duration you set in"
" GST Portal."
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "or"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "re-fetch"
msgstr ""

#. module: l10n_in_reports_gstr
#: model_terms:ir.ui.view,arch_db:l10n_in_reports_gstr.l10n_in_gst_return_period_form_view
msgid "re-match"
msgstr ""