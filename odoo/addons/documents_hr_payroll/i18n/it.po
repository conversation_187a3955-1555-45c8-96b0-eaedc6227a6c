# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_payroll
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_hr_payroll
#: model:mail.template,body_html:documents_hr_payroll.mail_template_new_declaration
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, a new declaration file is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Gentile <t t-esc=\"object.name\"/>, è disponibile una nuova dichiarazione.<br/><br/>\n"
"            Puoi trovare il PDF nel portale dipendenti.<br/><br/>\n"
"            Buona giornata,<br/>\n"
"            il team Risorse umane\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__document_id
msgid "Document"
msgstr "Documento"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Document posting is not properly set in configuration"
msgstr "La pubblicazione del documento non è configurata adeguatamente"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Documents"
msgstr "Documenti"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_count
msgid "Documents Count"
msgstr "Numero documenti"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_enabled
msgid "Documents Enabled"
msgstr "Documenti Attivati"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_hr_payslips_tags
msgid "Documents Hr Payslips Tags"
msgstr "Etichette documenti buste paga RU"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_payroll_folder_id
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_payroll_folder_id
msgid "Documents Payroll Folder"
msgstr "Cartella documenti libro paga"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contratto dipendente"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDFs are gonna be posted in Documents shortly"
msgstr "I PDF verranno pubblicati nei documenti a breve"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Busta paga"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/res_company.py:0
msgid "Payroll"
msgstr "Libro paga"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr "Mixin dichiarazione libro paga"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr "Dichiarazione dipendente libro paga"

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.res_config_settings_view_form
msgid "Payroll Workspace"
msgstr "Spazio di lavoro libro paga"

#. module: documents_hr_payroll
#: model:mail.template,name:documents_hr_payroll.mail_template_new_declaration
msgid "Payroll: New Declaration"
msgstr "Libro paga: nuova dichiarazione"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_hr_payslips_tags
msgid "Payslip"
msgstr "Busta paga"

#. module: documents_hr_payroll
#: model:documents.tag,name:documents_hr_payroll.documents_tag_payslips
msgid "Payslips (HR)"
msgstr "Buste paga (HR)"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__pdf_to_post
msgid "Pdf To Post"
msgstr "PDF da pubblicare"

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Post PDFs"
msgstr "Pubblica PDF"

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_posted
msgid "Posted PDF"
msgstr "PDF pubblicati"

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_post
msgid "Queued PDF posting"
msgstr "PDF in coda in fase di pubblicazione"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__state
msgid "State"
msgstr "Stato"

#. module: documents_hr_payroll
#: model:mail.template,subject:documents_hr_payroll.mail_template_new_declaration
msgid "{{ object.name }}, a new declaration file is available for you"
msgstr "{{ object.name }}, è disponibile una nuova dichiarazione per te"
