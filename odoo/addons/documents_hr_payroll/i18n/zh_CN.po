# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_payroll
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr_payroll
#: model:mail.template,body_html:documents_hr_payroll.mail_template_new_declaration
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, a new declaration file is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            亲爱的<t t-esc=\"object.name\"/>：有一份新的声明文件可供使用。<br/><br/>\n"
"            请在你的员工页面访问相关 PDF。<br/><br/>\n"
"            祝生活愉快！<br/>\n"
"            人力资源团队\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__document_id
msgid "Document"
msgstr "文档"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Document posting is not properly set in configuration"
msgstr "配置中未正确设置文件发布"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Documents"
msgstr "文档"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_count
msgid "Documents Count"
msgstr "文件数量"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_enabled
msgid "Documents Enabled"
msgstr "已启用文件"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_hr_payslips_tags
msgid "Documents Hr Payslips Tags"
msgstr "记录人力资源部工资单标签"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_payroll_folder_id
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_payroll_folder_id
msgid "Documents Payroll Folder"
msgstr "工资文档文档夹"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "员工合同"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDFs are gonna be posted in Documents shortly"
msgstr "PDF 文件将很快发布在 \"文件 \"中"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "工资单"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/res_company.py:0
msgid "Payroll"
msgstr "工资"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr "工资申报混合组件"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr "工资员工申报"

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.res_config_settings_view_form
msgid "Payroll Workspace"
msgstr "工资工作区"

#. module: documents_hr_payroll
#: model:mail.template,name:documents_hr_payroll.mail_template_new_declaration
msgid "Payroll: New Declaration"
msgstr "工资新申报"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_hr_payslips_tags
msgid "Payslip"
msgstr "工资单"

#. module: documents_hr_payroll
#: model:documents.tag,name:documents_hr_payroll.documents_tag_payslips
msgid "Payslips (HR)"
msgstr "工资单（人力资源）"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__pdf_to_post
msgid "Pdf To Post"
msgstr "发送Pdf "

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Post PDFs"
msgstr "发送 PDF"

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_posted
msgid "Posted PDF"
msgstr "已发布 PDF"

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_post
msgid "Queued PDF posting"
msgstr "排队张贴 PDF"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__state
msgid "State"
msgstr "状态"

#. module: documents_hr_payroll
#: model:mail.template,subject:documents_hr_payroll.mail_template_new_declaration
msgid "{{ object.name }}, a new declaration file is available for you"
msgstr "{{对象.名称 }}}，一个新的声明文件可供您使用"
