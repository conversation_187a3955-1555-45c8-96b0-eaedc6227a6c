<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_pk" model="res.partner" forcecreate="1">
        <field name="name">PK Company</field>
        <field name="vat"/>
        <field name="street">38st Floor, Saddar, Sindh</field>
        <field name="street2">Unit 07 - 10, 38/F Yat Chau International Plaza</field>
        <field name="city">Karachi</field>
        <field name="state_id" ref="l10n_pk.state_pk_sd"/>
        <field name="country_id" ref="base.pk"/>
        <field name="zip"/>
        <field name="phone">+92215684711</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.pkexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_pk" model="res.company" forcecreate="1">
        <field name="name">PK Company</field>
        <field name="partner_id" ref="base.partner_demo_company_pk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_pk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_pk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>pk</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_pk')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
