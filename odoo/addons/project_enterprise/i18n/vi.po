# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_enterprise
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "%(partner)s has %(amount)s tasks at the same time."
msgstr "%(partner)s có %(amount)s nhiệm vụ cùng một lúc."

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid ""
"<i class=\"fa fa-exclamation-circle me-2\" role=\"img\" title=\"Dependency "
"warning\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-circle me-2\" role=\"img\" title=\"Cảnh báo phụ"
" thuộc\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_kanban_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_kanban_inherited
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\" invisible=\"not planned_date_begin\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow "
"icon\" title=\"Mũi tên\" invisible=\"not planned_date_begin\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Mũi tên\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "<i class=\"fa fa-random me-2\" role=\"img\" title=\"Planning overlap\"/>"
msgstr "<i class=\"fa fa-random me-2\" role=\"img\" title=\"Lập kế hoạch chồng chéo\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> Private</span>"
msgstr ""
"<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> Riêng "
"tư</span>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>Thời gian được phân bổ — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Assignees — </strong>"
msgstr "<strong>Người được phân công — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Customer — </strong>"
msgstr "<strong>Khách hàng — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Hạn chót:</strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Milestone — </strong>"
msgstr "<strong>Mốc — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr "<strong>Ngày được lập kế hoạch:</strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
msgid "<strong>Project Manager — </strong>"
msgstr "<strong>Quản lý dự án — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Project — </strong>"
msgstr "<strong>Dự án — </strong>"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__allocated_hours
msgid "Allocated Time"
msgstr "Thời gian được phân bổ"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__user_ids
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Assignees"
msgstr "Người được phân công"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/view_dialogs/select_auto_plan_create_dialog.xml:0
msgid "Auto Plan"
msgstr "Tự động lập kế hoạch"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Check it out <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Xem <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Customer"
msgstr "Khách hàng"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Date"
msgstr "Ngày"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Milestone"
msgstr "Mốc thời gian"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"One parameter is missing to use this method. You should give a start and end"
" dates."
msgstr ""
"Thiếu một tham số để sử dụng phương pháp này. Bạn nên đặt ngày bắt đầu và "
"ngày kết thúc."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare dependency_warning to "
"True or False."
msgstr ""
"Thao tác không được hỗ trợ, bạn phải luôn so sánh dependency_warning với "
"Đúng hoặc Sai."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare planning_overlap to True "
"or False."
msgstr ""
"Thao tác không được hỗ trợ, bạn phải luôn so sánh planning_overlap với Đúng "
"hoặc Sai."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_form_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Planned Date"
msgstr "Ngày theo kế hoạch"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_start
msgid "Planned Date Start"
msgstr "Ngày bắt đầu theo kế hoạch"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Planning"
msgstr "Kế hoạch"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Private"
msgstr "Riêng tư"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_project
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__project_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Project"
msgstr "Dự án"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project due"
msgstr "Dự án đến hạn"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project start"
msgstr "Dự án bắt đầu"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Save"
msgstr "Lưu"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Some tasks were planned after their initial deadline."
msgstr "Một số nhiệm vụ đã được lên kế hoạch sau hạn chót ban đầu."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks were scheduled concurrently, resulting in a conflict due to the "
"limited availability of the assignees. The planned dates for these tasks may"
" not align with their allocated hours."
msgstr ""
"Một số nhiệm vụ được lên lịch đồng thời, dẫn đến xung đột do khung giờ trống"
" của người được phân công có hạn. Ngày được lên kế hoạch ​​cho những nhiệm "
"vụ này có thể không tương ứng với số giờ được phân bổ."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks weren't planned because the closest available starting date was "
"too far ahead in the future"
msgstr ""
"Một số nhiệm vụ không được lên kế hoạch vì ngày bắt đầu gần nhất có thể chọn"
" nằm quá xa trong tương lai"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_begin
#: model:ir.model.fields,field_description:project_enterprise.field_report_project_task_user__planned_date_begin
msgid "Start date"
msgstr "Ngày bắt đầu"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task
msgid "Task"
msgstr "Nhiệm vụ"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Nhiệm vụ định kỳ"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Phân tích nhiệm vụ"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks Scheduled"
msgstr "Nhiệm vụ đã lên lịch"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Tasks have been successfully scheduled for the upcoming periods."
msgstr "Nhiệm vụ đã được lên kế hoạch thành công cho giai đoạn sắp tới."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks in Conflict"
msgstr "Nhiệm vụ bị xung đột"

#. module: project_enterprise
#: model:ir.model.constraint,message:project_enterprise.constraint_project_task_planned_dates_check
msgid "The planned start date must be before the planned end date."
msgstr "Ngày bắt đầu theo kế hoạch phải trước ngày kết thúc theo kế hoạch."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"The tasks could not be rescheduled due to the assignees' lack of "
"availability at this time."
msgstr ""
"Không thể lên lịch lại nhiệm vụ do người được phân công không có khung giờ "
"trống vào thời điểm này."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This Progress Bar is not implemented."
msgstr "Thanh tiến trình này không được triển khai."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This task cannot be planned before the following tasks on which it depends: "
"%(task_list)s"
msgstr ""
"Không thể lập kế hoạch cho nhiệm vụ này trước các nhiệm vụ mà nó phụ thuộc "
"sau đây: %(task_list)s"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This user isn't expected to have any tasks assigned during this period "
"because they don't have any running contract."
msgstr ""
"Người dùng này sẽ không được giao bất kỳ nhiệm vụ nào trong giai đoạn này vì"
" họ không có bất kỳ hợp đồng nào đang có hiệu lực."

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.res_config_settings_view_form
msgid "Timesheets"
msgstr "Bảng chấm công"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Unassigned"
msgstr "Chưa phân công"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Unschedule"
msgstr "Huỷ lịch trình"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_renderer.xml:0
msgid "View customer locations for your tasks"
msgstr "Xem vị trí của khách hàng cho nhiệm vụ của bạn"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Warning"
msgstr "Cảnh báo"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "👤 Unassigned"
msgstr "👤 Chưa được phân công"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "🔒 Private"
msgstr "🔒 Riêng tư"
