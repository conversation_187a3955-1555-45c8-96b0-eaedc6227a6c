# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_enterprise
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "%(partner)s has %(amount)s tasks at the same time."
msgstr "%(partner)s ha %(amount)s attività nello stesso orario."

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid ""
"<i class=\"fa fa-exclamation-circle me-2\" role=\"img\" title=\"Dependency "
"warning\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-circle me-2\" role=\"img\" title=\"Dependency "
"warning\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_kanban_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_kanban_inherited
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\" invisible=\"not planned_date_begin\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\" invisible=\"not planned_date_begin\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Freccia\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "<i class=\"fa fa-random me-2\" role=\"img\" title=\"Planning overlap\"/>"
msgstr "<i class=\"fa fa-random me-2\" role=\"img\" title=\"Planning overlap\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> Private</span>"
msgstr ""
"<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> "
"Privato</span>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>Tempo assegnato — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Assignees — </strong>"
msgstr "<strong>Assegnatari —</strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Customer — </strong>"
msgstr "<strong>Cliente — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Scadenza:</strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Milestone — </strong>"
msgstr "<strong>Milestone — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr "<strong>Data pianificata:</strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
msgid "<strong>Project Manager — </strong>"
msgstr "<strong>Project Manager — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Project — </strong>"
msgstr "<strong>Progetto — </strong>"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__allocated_hours
msgid "Allocated Time"
msgstr "Tempo assegnato"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__user_ids
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Assignees"
msgstr "Assegnatari"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/view_dialogs/select_auto_plan_create_dialog.xml:0
msgid "Auto Plan"
msgstr "Pianificazione automatica"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Check it out <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Dai un'occhiata <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Customer"
msgstr "Cliente"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Date"
msgstr "Data"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Discard"
msgstr "Abbandona"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Milestone"
msgstr "Milestone"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"One parameter is missing to use this method. You should give a start and end"
" dates."
msgstr ""
"Manca un paramentro per utilizzare questo metodo. Inserisci una data di "
"inizio e di fine."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare dependency_warning to "
"True or False."
msgstr ""
"Operazione non supportata, devi sempre confrontare dependency_warning con "
"Vero o Falso."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare planning_overlap to True "
"or False."
msgstr ""
"Operazione non supportata, devi sempre confrontare planning_overlap con Vero"
" o Falso."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_form_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Planned Date"
msgstr "Data pianificata"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_start
msgid "Planned Date Start"
msgstr "Inizio data pianificata"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Planning"
msgstr "Pianificazione"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Private"
msgstr "Privato"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_project
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__project_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Project"
msgstr "Progetto"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project due"
msgstr "Progetto dovuto"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project start"
msgstr "Inizio progetto"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Save"
msgstr "Salva"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Some tasks were planned after their initial deadline."
msgstr "Alcune attività sono state pianificate dopo la scadenza iniziale."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks were scheduled concurrently, resulting in a conflict due to the "
"limited availability of the assignees. The planned dates for these tasks may"
" not align with their allocated hours."
msgstr ""
"Alcune attività sono state programmate contemporaneamente, portando ad un "
"conflitto dovuto alla disponibilità limitata degli assegnatari. Le date "
"pianificate per le attività potrebbero non essere allineate con le ore "
"assegnate."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks weren't planned because the closest available starting date was "
"too far ahead in the future"
msgstr ""
"Alcune attivita non sono state pianificate in quanto la data di inizio "
"disponibile più vicina era troppo lontana"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_begin
#: model:ir.model.fields,field_description:project_enterprise.field_report_project_task_user__planned_date_begin
msgid "Start date"
msgstr "Data inizio"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task
msgid "Task"
msgstr "Lavoro"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Ricorrenza lavoro"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analisi lavori"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks Scheduled"
msgstr "Lavori pianificati"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Tasks have been successfully scheduled for the upcoming periods."
msgstr ""
"Le attività sono state programmate con successo per i periodi in arrivo."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks in Conflict"
msgstr "Lavori in conflitto"

#. module: project_enterprise
#: model:ir.model.constraint,message:project_enterprise.constraint_project_task_planned_dates_check
msgid "The planned start date must be before the planned end date."
msgstr ""
"La data di inizio pianificata deve precedere la data di fine pianificata."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"The tasks could not be rescheduled due to the assignees' lack of "
"availability at this time."
msgstr ""
"Non è stato possibile riprogrammare le attività per via della mancanza di "
"disponibilità degli assegnatari in quel momento."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This Progress Bar is not implemented."
msgstr "L'indicatore di stato non è implementato."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This task cannot be planned before the following tasks on which it depends: "
"%(task_list)s"
msgstr ""
"L'attività non può essere pianificata prima delle attività da cui dipende: "
"%(task_list)s"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This user isn't expected to have any tasks assigned during this period "
"because they don't have any running contract."
msgstr ""
"Non sono previste attività assegnate all'utente durante questo periodo "
"perché non presentano contratti in essere."

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.res_config_settings_view_form
msgid "Timesheets"
msgstr "Fogli ore"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Unassigned"
msgstr "Non assegnati"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Unschedule"
msgstr "Annullare"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_res_users
msgid "User"
msgstr "Utente"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_renderer.xml:0
msgid "View customer locations for your tasks"
msgstr "Mostra posizione clienti per le attività"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Warning"
msgstr "Attenzione"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "👤 Unassigned"
msgstr "👤 Non assegnato"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "🔒 Private"
msgstr "🔒 Privato"
