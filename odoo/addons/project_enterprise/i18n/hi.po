# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_enterprise
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>doo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "%(partner)s has %(amount)s tasks at the same time."
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid ""
"<i class=\"fa fa-exclamation-circle me-2\" role=\"img\" title=\"Dependency "
"warning\"/>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_kanban_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_kanban_inherited
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\" invisible=\"not planned_date_begin\"/>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "<i class=\"fa fa-random me-2\" role=\"img\" title=\"Planning overlap\"/>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> Private</span>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Assignees — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Customer — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Milestone — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
msgid "<strong>Project Manager — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Project — </strong>"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__allocated_hours
msgid "Allocated Time"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__user_ids
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Assignees"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/view_dialogs/select_auto_plan_create_dialog.xml:0
msgid "Auto Plan"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Check it out <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Customer"
msgstr "साथी"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Date"
msgstr "तिथि"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Discard"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Milestone"
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"One parameter is missing to use this method. You should give a start and end"
" dates."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare dependency_warning to "
"True or False."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Operation not supported, you should always compare planning_overlap to True "
"or False."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_form_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Planned Date"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_start
msgid "Planned Date Start"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Planning"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Private"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_project
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__project_id
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Project"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project due"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/milestones_popover.xml:0
msgid "Project start"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Save"
msgstr "सहेज"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Some tasks were planned after their initial deadline."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks were scheduled concurrently, resulting in a conflict due to the "
"limited availability of the assignees. The planned dates for these tasks may"
" not align with their allocated hours."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"Some tasks weren't planned because the closest available starting date was "
"too far ahead in the future"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_begin
#: model:ir.model.fields,field_description:project_enterprise.field_report_project_task_user__planned_date_begin
msgid "Start date"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task
msgid "Task"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_report_project_task_user
msgid "Tasks Analysis"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks Scheduled"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Tasks have been successfully scheduled for the upcoming periods."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_search_conflict_task_project_enterprise
msgid "Tasks in Conflict"
msgstr ""

#. module: project_enterprise
#: model:ir.model.constraint,message:project_enterprise.constraint_project_task_planned_dates_check
msgid "The planned start date must be before the planned end date."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"The tasks could not be rescheduled due to the assignees' lack of "
"availability at this time."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This Progress Bar is not implemented."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This task cannot be planned before the following tasks on which it depends: "
"%(task_list)s"
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This user isn't expected to have any tasks assigned during this period "
"because they don't have any running contract."
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.res_config_settings_view_form
msgid "Timesheets"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_model.js:0
msgid "Unassigned"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Unschedule"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_res_users
msgid "User"
msgstr "उपयोगकर्ता"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/project_task_map/project_task_map_renderer.xml:0
msgid "View customer locations for your tasks"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_renderer.js:0
msgid "Warning"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "👤 Unassigned"
msgstr ""

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/views/task_gantt/task_gantt_model.js:0
msgid "🔒 Private"
msgstr ""
