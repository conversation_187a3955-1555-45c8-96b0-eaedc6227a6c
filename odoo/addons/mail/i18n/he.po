# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON>, 2024
# שהא<PERSON>י<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# ya<PERSON> terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# david <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> BLONDER <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# NoaFarkash, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# tomerlayline, 2024
# <PERSON>ch <PERSON><PERSON> <<EMAIL>>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "\"%(hostname)s\" requires microphone access"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "%(author name)s from %(channel name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "%(candidateType)s (%(protocol)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr "%(email)s לא מוכר ככתובת מייל מאומתת. זה דרוש ליצירת לקוח חדש."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s and %(person2)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s "
"others"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "%(model_name)s.%(field_path)s does not seem to be a valid field path"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%(name)s: %(message)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.js:0
msgid "%(recipientCount)s more"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(user)s started a thread: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sSee all threads%(goto_all_end)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s and %(user2)s are typing..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s, %(user2)s and more are typing..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "%(user_name)s pinned a message to this channel."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
msgid "%s (Email Template)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan.py:0
#: code:addons/mail/models/mail_template.py:0
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "%s created"
msgstr "%s נוצר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "%s days overdue"
msgstr "%s ימי איחור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%s is typing..."
msgstr "1%s מקליד/ה..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_result.js:0
msgid "%s messages found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "%s new messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s raised their hand"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "%s started a live conference"
msgstr "%s התחיל שיחת ועידה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"camera\" access"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"screen recording\" access"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_attachment_links
msgid "&amp;#128229;"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translated from: %(language)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translation Failure: %(error)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "(edited)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "(from"
msgstr "(מ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(הוטל במקור על"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid ") added you as a follower of this"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid ". Narrow your search to see more choices."
msgstr ". עשו חיפוש יותר ממוקד כדי להרחיב את האפשרויות."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "1 new message"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid ""
"<button>Change your preferences</button> to receive new notifications in "
"your inbox."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-check\"/> Done"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_kanban
msgid ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<b>דברו עם הצוות בזמת-אמת ובהודעות ישירות<p><p><i>אולי תרצו קודם להזמין "
"משתמשים מאיזור ההגדרות</i></p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>כתבו כאן הודעה</b> לחברי הערוץ.</p><p>אפשר להודיע למישהו עם <i>'@'</i>"
" או לקשר לערוץ אחר עם <i>'#'</i>. התחילו את ההודעה עם '/' לקבלת רשימה של "
"פעולות אפשריות.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>ערוצים מאפשרים לסדר מידע בנושאים שונים מקבוצות שונות</p><p>נסו<b>ליצור את"
" הערוץ הראשון שלך</b> (למשל מכירות, שיווק, מוצרים XYZ, מסיבת צוות, "
"וכו').</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a channel here.</p>"
msgstr "<p>לחצו כאן ליצירת ערוץ</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>יצירת ערוץ פרטי או ציבורי</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">צבע תחתון</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">צבע כותרת עליונה</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""

#. module: mail
#: model_terms:web_tour.tour,rainbow_man_message:mail.discuss_channel_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>עבודה מעולה!</b> עברת על כל השלבים של המדריך.</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong>שמור</strong> דף זה וחזור לכאן כדי להגדיר את התכונה."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"מילון פייתון שיוערך כדי לספק ערכי ברירת מחדל בעת יצירת רשומות חדשות לכינוי "
"זה."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A Scheduled Message cannot be scheduled in the past"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "חבר בערוץ צריך להיות שותף או אורח."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "סוג הערוץ 'צ'אט' יכול להכיל עד שני משתמשים."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "צ'אט לא מיועד ליותר מ-2 משתמשים. במקום זה, צריך ליצור קבוצה."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "A message can only be scheduled in monocomment mode"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"A message cannot be scheduled on a model that does not have a mail thread."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "תגובה להודעה חייבת להיות משותף או מאורח."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "A next activity can only be planned on models that use activities."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A scheduled message could not be sent"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "הגדרות עוצמת שמע צריכה שיהיה שותף או אורח בשיחה."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_chatgpt.js:0
msgid "AI"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept"
msgstr "אשר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept with camera"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Access Denied"
msgstr "הגישה נדחתה"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "קבוצות הרשאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "אסימון גישה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Account"
msgstr "חשבון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "פעולה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "תצוגת חלון פעולה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Actions"
msgstr "פעולות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"פעולות עשויות להפעיל התנהגות מסוימת כמו פתיחת תצוגת לוח שנה או סימון אוטומטי"
" כבוצע בעת העלאת המסמך"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "פעולות לבצע על דואר נכנס"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "מופעל כברירת מחדל כשמצטרפים."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "פעיל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "דומיין פעיל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities_section
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activities"
msgstr "פעילויות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Create"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity"
msgstr "פעילות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.ui.menu,name:mail.menu_mail_activities
msgid "Activity Overview"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "תכניות פעילויות "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "הגדרות פעילות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "סוג פעילות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Activity Type Name"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Prospect Follow-up\", \"Project Milestone Meeting\", ...)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Activity type"
msgstr "סוג פעילות"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Activity: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "הוסף רשימה שחורה של דוא\"ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add Followers"
msgstr "הוסף עוקבים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.xml:0
msgid "Add Reaction"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Add a Reaction"
msgstr "הוספת תגובה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Add a description"
msgstr "הוסף תיאור"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "הוספת %(document)s חדש או שליחת מייל ל-%(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "הוספת מייל לרשימה השחורה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add and close"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Add as recipient and follower (reason: %s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Add contacts to notify..."
msgstr "הוסף אנשי קשר שברצונך ליידע..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
msgid "Add followers to this document"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Add or join a channel"
msgstr "הוספה או הצטרפות לערוץ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "הוסף את אישורי twilio שלך עבור שרתי ICE"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "אי אפשר להוסיף עוקבים לערוץ. נסו להוסיף חברים במקום."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "אנשי קשר נוספים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "מתקדם"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "אפשרויות מתקדמות"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__after_plan_date
msgid "After Plan Date"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "אזהרה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "כינוי"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "כינוי אבטחה של איש קשר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "שם-מתחם (דומיין)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "כינוי שם (דומיין)"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "קידומת מייל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "שם כינוי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "סטטוס"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "מודל בעל כינוי"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "כינויים"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "All"
msgstr "הכל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__all
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__all
msgid "All Messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "All conversations have been muted"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "All partners must belong to the same message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "An access token must be provided for each attachment."
msgstr "יש לספק אסימון גישה לכל קובץ מצורף."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "An email is required for find_or_create to work"
msgstr "צריך כתובת מייל כדי שיעבוד find_or_create"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email on “%(record_name)s”"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "An error occurred while fetching messages."
msgstr "קרתה שגיאה בייבוא הודעות."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "An unexpected error occurred during the creation of the chat."
msgstr "שגיאה לא צפויה התרחשה בנסיון ליצור צ'אט."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And"
msgstr "וגם"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And 1 other member."
msgstr "ועוד משתמש/ת 1."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "חל על"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
msgid "Apply"
msgstr "החל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "בארכיון"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Are you sure you want to cancel the scheduled message?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are you sure you want to delete \"%(template_name)s\"?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Are you sure you want to delete this message?"
msgstr "האם אתה בטוח שברצונך למחוק את ההודעה הזו?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "האם אתה בטוח שברצונך להסיר את כתובות המייל האלה מהרשימה השחורה?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Are you sure you want to unblacklist this email address?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are your sure you want to update \"%(template_name)s\"?"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to ..."
msgstr "משוייך ל ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to me"
msgstr "שייך אלי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Assigned To"
msgstr "משויכת ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
msgid "Assigned to"
msgstr "משויכת ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "משימה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "At this point lang should be correctly set"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Attach files"
msgstr "צירוף מסמכים"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "קובץ מצורף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Attachment counter loading..."
msgstr "טוען מונה צרופות..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr "קבצים מצורפים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Audio player:"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "לקוחות/ספקים מאומתים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "מחבר"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"מחבר ההודעה. אם לא מוגדר, email_from עשוי להכיל כתובת דוא\"ל שלא התאימה לשום"
" לקוח/ספק."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "דמות היוצר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "קבוצה מורשית"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__group_ids
msgid "Authorized Groups"
msgstr "קבוצות מורשות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "מחיקה אוטומטית"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "הרשמה אוטומטית לקבוצות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "מנוי אוטומטי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "מנוי אוטומטי"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "פעולה אוטומטית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Automated message"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"תזמון ביצוע פעולה זו אוטומטית ברגע שהפעולה הנוכחית מסומבנת שהיא \n"
"בוצעה."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
msgid "Avatar"
msgstr "אווטר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "אווטר 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "אווטר 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "אווטר 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "אווטר 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_cache_key
msgid "Avatar Cache Key"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Avatar of user"
msgstr "דמות של משתמש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Away"
msgstr "רחוק"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Background blur intensity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "בסיס"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__before_plan_date
msgid "Before Plan Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "רשימה שחורה "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "תאריך רשימה שחורה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "כתובת אנשי הרשימה השחורה"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "כתובות מייל של אנשי הרשימה השחורה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Blur Background"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Blur video background"
msgstr "טשטוש רקע וידאו"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "גוף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Bot"
msgstr "רובוט"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "דוא\"ל לא נמסר - חזר עם שגיאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
msgid "Bounced"
msgstr "לא נמסר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Browser default"
msgstr "דפדפן ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__endpoint
msgid "Browser endpoint"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__keys
msgid "Browser keys"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
msgid "CC Email"
msgstr "עותק דוא\"ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "CTRL"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "שיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Call Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Camera is off"
msgstr "המצלמה כבויה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "ניתן לערוך גוף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "ניתן לכתיבה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Can not update the message or recipient of a notification."
msgstr "אי אפשר לעדכן את ההודעה או את הנמען/ת."

#. module: mail
#: model:ir.model,name:mail.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Cancel"
msgstr "בטל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "ביטול הדוא\"ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Cancel Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
msgid "Cancelled"
msgstr "בוטל"

#. module: mail
#: model:ir.model,name:mail.model_mail_canned_response
msgid "Canned Response"
msgstr ""

#. module: mail
#: model:res.groups,name:mail.group_mail_canned_response_admin
msgid "Canned Response Administrator"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_canned_response_action
#: model:ir.module.category,name:mail.module_category_canned_response
#: model:ir.ui.menu,name:mail.menu_canned_responses
msgid "Canned Responses"
msgstr "קיצורי דרך לתגובות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Canned Responses Search"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_form
msgid "Canned response"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__source
msgid ""
"Canned response that will automatically be substituted with longer content "
"in your messages. Type ':' followed by the name of your shortcut (e.g. "
":hello) to use in your messages."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_tree
msgid "Canned responses"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                    your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                    replaced directly in your message, so that you can still edit\n"
"                    it before sending."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change initial message nor parent channel of: %(channels)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: initial message should belong to parent channel."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: parent should not be a sub-channel and should be"
" of type 'channel'."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "הודעה לנמענים בהעתק CC"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "נמענים בהעתק CC"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "נמענים בהעתק CC (אפשר להשתמש כאן בתבניות)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "תופס-כל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "תופס-כל מייל"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "CC"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "סוג שרשור"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "שינוי צבע הרקע של פעילויות קשורות לסוג זה."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Channel"
msgstr "ערוץ"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "חבר בערוץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
#: model:ir.model.fields,field_description:mail.field_res_users_settings__channel_notifications
msgid "Channel Notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "סוג ערוץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Channel full"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "Channel members cannot include public users."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Channel settings"
msgstr "הגדרות ערוץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
msgid "Channels"
msgstr "ערוצים"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
msgid "Chat"
msgstr "צ'אט"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Chat Options"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"צ'אט הוא פרטי וייחודי ל-2 משתמשים. קבוצה היא פרטית למוזמנים בלבד. ההצטרפות "
"לערוץ הנה חופשית (תלוי בהגדרות)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Chats"
msgstr "צ'אטים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "הודעות בנות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "בחר תבנית..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Choose another value or change it on the other document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Clear quick search"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Click here to retry"
msgstr "לחצו כאן לנסות שוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Click on your message"
msgstr "לחיצה על ההודעה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Click to see the attachments"
msgstr "לחיצה לצפות בצרופה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Close"
msgstr "סגור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "Close Chat Bubble"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Chat Window (ESC)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Search"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Close all conversations"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_ad_banner.xml:0
msgid "Close banner"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Close button"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
msgid "Close panel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
msgid "Close search"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "סגור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Collapse panel"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Come here often? Install the app for quick and easy access!"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "כתבו כאן נמענים ב-CC מופרדים בסימני-פסיק"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "מספרי הזיהוי של השותפים, מופרדים בסימני-פסיק"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"מספרי הזיהוי של השותפים, מופרדים בסימני-פסיק (ניתן להשתמש כאן בתבניות)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "כתובות נמענים מורדים בסימני פסיק"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "כתובות מוזמנים מופרדים בסימני פסיק (אפשר להשתמש כאן בתבניות)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "תגובה"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "חברות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "חברה"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr "כתוב דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "מצב כתיבה"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model:ir.ui.menu,name:mail.menu_configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "תצורה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "קבע את תצורת סוגי הפעולות שלך"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "הגדר את שרתי הדוא\"ל שלך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
msgid "Confirm"
msgstr "אשר"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Confirmation"
msgstr "אישור"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "מאושר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Congratulations, you're done with your activities."
msgstr "סחטיין, סיימת עם הפעולות."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Congratulations, your inbox is empty"
msgstr "ברכות, תיבת הדואר הנכנס שלך ריקה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_patch.js:0
msgid "Congratulations, your inbox is empty!"
msgstr "ברכות, תיבת הדואר הנכנס שלך ריקה!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "החיבור נכשל (בעיית שרת דואר יוצא)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Connection test failed: %s"
msgstr "בדיקת תקשורת נכשלה: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Connection to SFU server closed by the server"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection type:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection:"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "רואה בתשובות שרשור חדש"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "אנשי קשר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this user. This includes: creating, joining, pinning"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel. This updates itself when new message posted."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__unpin_dt
msgid "Contains the date and time when the channel was unpinned by the user."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "תוכן"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__body
msgid "Contents"
msgstr "תוכן"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "מצב הקיפול של השיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Copy Link"
msgstr "העתק קישור"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "מונה מספר הודעות הדוא\"ל שחזרו עבור איש קשר זה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "ארץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Create"
msgstr "יצירה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "צור פעילות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "תאריך יצירה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Create Group Chat"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Create Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "צור מזהה ייחודי"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Create a Mail Template"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "יצירת רשומה חדשה"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid "Create a new canned response"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s"
msgstr "צור %(document)s חדש"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "יצירת %(document)s חדש על-ידי שליחת מייל ל-%(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
msgid "Create: #"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Created"
msgstr "נוצר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "נוצר ע\"י"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/chatter.js:0
msgid "Creating a new record..."
msgstr "יוצר רשומה חדשה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "יוצר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "תעודות ואישורים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "למשתמש הנוכחי יש כוכב התראה המחובר להודעה זו."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "הודעה מותאמת אישית להודעות שגויות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "שם ייחודי לערוץ"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "נדרש לקוח לדואר נכנס / התראות מייל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "עצב את המראה והתצוגה של מיילים אוטומטיים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Data channel:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "תאריך"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "ימים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "תאריך יעד"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Deadline: %s"
msgstr "מועד אחרון: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Deafen"
msgstr "השתקה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "יקר"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Dear Sender"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "סוג סימון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
#: model_terms:ir.ui.view,arch_db:mail.mail_message_subtype_view_search
msgid "Default"
msgstr "ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "מצב תצוגה ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "הערת ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "תיאור ברירת מחדל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Default Summary:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "משתמש ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "ערכי ברירת מחדל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Default deadline for the activities..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "נמענים ברירת מחדל"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"נמענים מוגדרים כברירת מחדל:\n"
" - שותף (בשימוש מספר הזיהוי או השדה Partner_id) \n"
"או\n"
"- מייל (שימוש בשדה email_from)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "מגדיר את סדר הפעולות בעיבוד. מספרים נמוכים הם בעדיפות גבוהה יותר."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "תגית עיכוב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "סוג עיכוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Delay after releasing push-to-talk"
msgstr "עיכוב עד אחרי שחרור \"דיבור בלחיצה\""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "תקופת עיכוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Delete"
msgstr "מחיקה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "מחק הודעות דוא\"ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Delete Template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Delete all previews"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
msgid "Delivered"
msgstr "נמסר"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "השליחה נכשלה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Delivery failure"
msgstr "שליחה נכשלה"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "תיאור"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr "תיאור שיופיע להודעה שתשלחה מתת-סוג זה. אם ריק, השם יתווסף במקום."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"קובע איך הערוץ יוצג כברירת מחדל כשפותחים אותו מקישור הזמנה. אם ריק יציג רק "
"טקסט (בלי קול / וידאו)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_editable
msgid "Determines if the canned response can be edited by the current user"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_shared
msgid "Determines if the canned_response is currently shared with other users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Direct messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Discard"
msgstr "בטל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "Disconnect"
msgstr "התנתק"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Disconnected from the RTC call by the server"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "צ'אט"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "תפריט צד דיון"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_users_settings_unmute_ir_actions_server
msgid "Discuss: users settings unmute"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "ערוץ דיון"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "דיונים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Dismiss"
msgstr "שחרר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr "הצג אפשרות במסמכים קשורים לפתיחת אשף חיבור עם תבנית זו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
msgid "Display avatar name"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
msgid "Do you really want to delete \"%s\"?"
msgstr "באמת ברצונך למחוק את \"%s\"?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Do you really want to delete this preview?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "מסמך"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "עוקבים אחר המסמך"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "מודל מסמך"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "שם מסמך"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "דומיין"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done"
msgstr "בוצע"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "בוצע ומפעיל את הבא"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Schedule Next"
msgstr "בצע ותזמן פעילות הבאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done Date"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Done and Schedule Next"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Download"
msgstr "הורד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Download Files"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Download logs"
msgstr "הורדת יומנים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Download:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/mail_attachment_dropzone.xml:0
msgid "Drop Files here"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "תאריך פירעון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "תאריך יעד "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due in"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Due in %s days"
msgstr "בתוקף עוד %s ימים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due on"
msgstr "לביצוע ב:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "סוג יעד"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "אימייל משוכפל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "משך פעילות קולים במילי-שניות ms"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "דוחות דינמיים"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Edge blur intensity"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Edit"
msgstr "ערוך"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "ערוך שותפים"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Message"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Note"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "Edit Subscription of %(name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Edit subscription"
msgstr "ערוך מנוי"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
msgid "Edit: %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "הוספת חתימה במייל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "כתובת דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "קידומת מייל"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "כינויי דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "ערבוב כינויי דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "רשימה שחורה של דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "צבע לחצן דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "ניהול עותקי CC במייל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "הגדרת תצורה דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Email Failure: %(modelName)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "צבע כותרת דוא\"ל"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "שליחת מייל המוני"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "פריסת הודעות במייל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "תצוגה מקדימה לדוא\"ל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "חיפוש דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "תבנית דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "צפיה מקדימה לתבניות מייל"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "תבניות דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "שרשור דוא\"ל"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "כתובת האימייל קיימת כבר!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"כתובת המייל של השולח. שדה זה מוגדר כאשר לא נמצא שותף תואם ומחליף את השדה "
"author_id בצ'אט."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "כתובת המייל אליה יופנו העונים למייל המוני"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"כתובת המייל אליה יופנו העונים למייל המוני: לשימוש רק אם התשובה לא רשומה "
"בשרשור הדיון המקורי."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "כתובות מייל ברשימה השחורה לא יגבלו הודעות מייל יותר."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "עותק דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "אשף יצירת דוא\"ל"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "הודעת דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "אשף שליחת מייל מחדש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "תבניות דוא\"ל"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "הודעות דוא\"ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "Emoji"
msgstr "אימוג'י"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
msgid "Emojis"
msgstr "אימוג'ים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "עובדים וצוות בלבד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Enable"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "לאפשר מעקב אחרי הזמנה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Enter"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Enter Full Screen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
msgid "Error"
msgstr "שגיאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "הודעת שגיאה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
msgid "Error during communication with the publisher warranty server."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "הודעת שגיאה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "שגיאה, שותף לא יכול לעקוב פעמיים אחרי אותו דבר."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "כולם"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "אחר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Exit Fullscreen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Expand"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Expand options"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Expand panel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__expiration_time
msgid "Expiration Token Date"
msgstr "תאריך תפוגת האסימון"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "מסננים מורחבים..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Extra Comments ..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "מייל כושל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "נכשל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid "Failed to enable push notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Failed to load gifs..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Failed to post the message. Click to retry"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render template: %(view_ref)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "סיבת כישלון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "סיבת כישלון"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "סוג שגיאה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Failure: %(modelName)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "מועדף על ידי"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Favorites"
msgstr "מועדפים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Feedback:"
msgstr "משוב"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "לייבא עכשיו"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "שדה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "אי אפשר לשנות שדה \"פעילות מייל\" ל\"לא נכון\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "אי אפשר לשנות שדה \"רשימה שחורה של מיילים\" ל\"לא נכון\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "אי אפשר לשנות שדה \"שרשור מייל\" ל\"לא נכון\"."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "פרטי שדה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"שדות משמשים לקשר בין מודל לתת-סוג מודל, כשימוש במנויים אוטומטיים למסמך "
"מקושר. השדה משמש לחישוב getattr(related_document.relation_field)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "שדות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "File too large"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is disabled for external users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is enabled for external users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Files"
msgstr "קבצים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Fold"
msgstr "קפל"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "מקופל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Follow"
msgstr "עקוב"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "עוקבים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "טופס עוקבים"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "עוקבים בלבד"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Following"
msgstr "עוקבים"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 1 hour"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 15 minutes"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 24 hours"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 3 hours"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 8 hours"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "דוא\"ל מעוצב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "מ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__from_message_id
msgid "From Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "From peer:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Full composer"
msgstr "הגדל אזור כתיבה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "וידאו במסך מלא"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Future"
msgstr "עתידי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Category"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Favorites"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "GIFs"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "שער"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Go to conversation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "קבוצה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "שם הקבוצה"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "קבץ לפי..."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_sub_channel_no_group_public_id
msgid ""
"Group public id should not be set on sub-channels as access is based on "
"parent channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Grouped Chat"
msgstr "צ'אט קבוצתי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "קבוצות"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
msgid "Guest"
msgstr "אורח"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name cannot be empty."
msgstr "שם האורח לא יכול להיות ריק"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name is too long."
msgstr "שם האורח ארוך מדי."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "אורחים"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "ניתוב HTTP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "יש שרשור דואר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "אזכורים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "השתיק שמע נכנס"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "יש שגיאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "יש על פי דרישה אחראי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "כותרות עליונות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "שלום"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "נסתר"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
msgid "Hide Pinned Messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Hide all conversations"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Hide sidebar"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "הסתירו את תת-הסוג באפשרויות העוקב"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "גבוה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "History"
msgstr "היסטוריה"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "כתובת או IP של שרת המייל"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr "כמה זמן שידור השמע יישאר פעיל אחרי שעובר את סף העוצמה."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE gathering:"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_push__id
#: model:ir.model.fields,field_description:mail.field_mail_push_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "מזהה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"מזהה של רשומת האב המחזיקה בכינוי (דוגמה: פרויקט המחזיק בכינוי ליצירת "
"המשימות)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "סטטוס IM"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "שרת IMAP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "זהות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Idle"
msgstr "מתבטל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "אם דרוש SSL."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__mute_until_dt
msgid ""
"If set, the user will not receive notifications from all the channels until "
"this date."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"אם מוגדר, תוכן זה יישלח אוטומטית למשתמשים לא מורשים במקום הודעת ברירת המחדל."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"אם כתובת הדואר האלקטרוני נמצאת ברשימה השחורה, איש הקשר לא יקבל עוד דיוור "
"המוני, מאף רשימה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_scheduled_message__is_note
msgid "If the message will be posted as a Note."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "תמונה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "תמונה 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "תמונה 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "תמונה 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "תמונה 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "התמונה היא קישור"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Inactive Alias"
msgstr "כינוי לא פעיל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "שרת מייל נכנס"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
msgid "Inbox"
msgstr "דואר נכנס"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Call..."
msgstr "שיחה נכנסת..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "שרתי דואר נכנס"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "שרת דואר נכנס"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "שרתי דואר נכנס"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Video Call..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "מציין שפעולה זו נוצרה אוטומטית ולא על-ידי משתמש כלשהו."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Info"
msgstr "מידע"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "מודל ראשוני"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "כתובת מלאה מוטבעת"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Input device"
msgstr "מכשיר שמע"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Insert Template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Insert Templates"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Install"
msgstr "להתקין"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Install Odoo"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "אינטגרציות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "פנימי בלבד"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_count
msgid "Interval"
msgstr "מרווח"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "לא תקין"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Invalid domain “%(domain)s” (type “%(domain_type)s”)"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "כתובת אימייל לא חוקית"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Invalid email address “%s”"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Invalid primary email field on model %s"
msgstr "שדה מייל ראשי לא תקין במודל %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr "ערך לא תקין ביצירת ערוץ עם משתמשים, אפשר רק 4 או 6."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "ערך לא תקין ביצירת ערוץ עם חנויים, מותר רק 0."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "הזמנה לעקוב אחרי %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite"
msgstr "הזמן"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Invite People"
msgstr "להזמין אנשים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Invite a User"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Invite people"
msgstr "הזמן אנשים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Channel"
msgstr "הזמנה לערוץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Group Chat"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "אשף הזמנה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "האם פעיל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "משתמש נוכחי או יוצר אורח"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "ניתן לעריכה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "האם עורך"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__is_hidden
msgid "Is Hidden"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "חבר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "נקרא"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__is_note
msgid "Is a note"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "האם המיקורופון מושתק"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "שלוח וידאו למשתמש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "בשיתוף מסך"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_push_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "הצטרפות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Join Call"
msgstr "הצטרף לשיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Join Channel"
msgstr "הצטרפות לערוץ"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "הצטרף לקבוצה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
msgid "Jump"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Jump to Present"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "שמירת צרופות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "חזור על הפעילות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "שמור על המקור"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "מפתח"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Kind Regards"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "LIVE"
msgstr "בשידור חי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "שפה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "תאריך ייבוא אחרון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "נבדק לאחרונה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__last_interest_dt
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "עניין אחרון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "נראה לאחרונה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "עודכן לאחרונה ב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__last_used
msgid "Last Used"
msgstr "בשימוש אחרון "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__last_used
msgid "Last time this canned_response was used"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Late"
msgstr "מאחר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Launch Plans"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "עיצוב"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "עזוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Leave this channel"
msgstr "לעזוב את הערוץ הזה"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Link copied!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "List Activity"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "List users in the current channel"
msgstr "רשימת משתמשים בערוץ הנוכחי"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Load More"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Load more"
msgstr "טען עוד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/navigable_list.xml:0
#: code:addons/mail/static/src/core/public_web/messaging_menu.xml:0
msgid "Loading…"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "שרת מקומי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "זיהוי נכנסות מבוסס חלק מקומי"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Log"
msgstr "כתוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Log Later"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Log Now"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Log RTC events"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "כתוב הערה..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "כתוב פעילות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Log an internal note…"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Log note"
msgstr "כתוב הערה פנימית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Log step:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
msgid "Logged in as %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "פרטי התחברות"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "נמוך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Lower Hand"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
msgid "Mail"
msgstr "דואר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "פעילות מייל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "סוג פעילות דואר"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "רשימה שחורה של מיילים"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "טופס רשימה שחורה של מיילים"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mail Failures"
msgstr "כשלונות מייל"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "Mail Layout"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "דואר Render Mixin"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "שרת דואר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "תבנית דוא\"ל"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "עריכת תבנית דוא\"ל"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "שרשור מייל"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "מספר מעקב דואר"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "נוצר מייל להודעה לאנשים על הודעה קיימת mail.message"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "Mail template model of %(action_name)s does not match action model."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "מייל: שירות ייבוא מייל"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_post_scheduled_message_ir_actions_server
msgid "Mail: Post scheduled messages"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Mailbox unavailable - %s"
msgstr "תיבת דואר נכנס לא זמינה - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mailboxes"
msgstr "תיבות דואר נכנס"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "מיילים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "קובץ ראשי מצורף "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "נהל תשובות כהודעות דוא\"ל נכנסות חדשות במקום תשובות שיעברו לאותו שרשור."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Mark As Read"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Mark Done"
msgstr "סמן כבוצע"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Mark all read"
msgstr "סמן הכל כנקרא"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Mark as Done"
msgstr "סמן כבוצע"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Mark as Read"
msgstr "סימון כנקרא"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Todo"
msgstr "סמן לביצוע  "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Unread"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Mark as done"
msgstr "סמן כבוצע"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "אמצעי תקשורת"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "פגישה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "מספר משתתפים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "אנשי צוות"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"חברים בקבוצות אלו יתווספו אוטומטית כעוקבים. שימוב לה שהם יוכלו לנהל את "
"המנויים שלהם ידנית אם ירצו."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Mentions"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
msgid "Mentions Only"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_ui_menu
msgid "Menu"
msgstr "תפריט"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "אשף מיזוג לקוחות"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
msgid "Merged with the following partners: %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Message"
msgstr "הודעה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message \"%(subChannelName)s\""
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message #%(threadName)s…"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message %(thread name)s…"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "מספר זיהוי הודעה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copied!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "התראות הודעה"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "תגובה להודעה"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "תגובות להודעה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "שם רשומת הודעה"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "מפתח API לתרגום הודעות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "סוג ההודעה"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__new_message_separator
msgid "Message id before which the separator should be displayed"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message posted on \"%s\""
msgstr "הודעה פורשמה ב\"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "נמענים (דוא\"ל)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "הפניות להודעות, למשל מספר זיהוי של הודעה קודמת"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Message should be a valid EmailMessage instance"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "תת-סוגי הודעות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"סוג הודעה: אימייל להודעת דואר אלקטרוני, התראה על הודעת מערכת, הערה להודעות "
"אחרות כגון תשובות משתמשים"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "מספר זיהוי ייחודי להודעה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Message-Id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "הודעות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "חיפוש הודעות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr "הודעות יכולות <b>לככב</b> כדי להזכיר לך לבדוק מה קורה עם זה."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_from_message_id_unique
msgid "Messages can only be linked to one sub-channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Messages marked as read will appear in the history."
msgstr "הודעה שסומנה כנקראה יופיעו בהיסטוריה."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Messages with tracking values cannot be modified"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "חסר מייל"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "כתובת אימייל חסרה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "מודל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "המודל השתנה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "מודל המשאב שאחריו"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "מודלים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"שינוי המודל יכול להשפיע על פעולות קיימת המשתמשות בסוג פעולה זו, יש לפעול "
"בזהירות."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "הסרת מודול"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "חודשים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
msgid "More"
msgstr "עוד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Mute"
msgstr "השתק"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Mute Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute all conversations"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute duration"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
#: model:ir.model.fields,field_description:mail.field_res_users_settings__mute_until_dt
msgid "Mute notifications until"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Muting prevents unread indicators and notifications from appearing."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action_my
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "My Activities"
msgstr "הפעילויות שלי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "התבניות שלי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "My canned responses"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "שם"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "נדרשת פעולה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "New"
msgstr "חדש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Channel"
msgstr "ערוץ חדש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Message"
msgstr "הודעה חדשה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__new_message_separator
msgid "New Message Separator"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "New Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "ערך חדש - תו"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "ערך חדש - תאריך-שעה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "ערך מספרי חדש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "טקסט ערך חדש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "New message"
msgstr "הודעה חדשה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "New messages appear here."
msgstr "הודעות חדשות מופיעות כאן."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "ערכים חדשים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "הפעילויות הבאות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "הפעילות הבאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Next Monday Morning"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "פעולות חדשות זמינות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "No"
msgstr "לא"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "No Error"
msgstr "אין שגיאה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "No Followers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.js:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "No IM status available"
msgstr "אין סטטוס IM זמין"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "אין רשומה"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No channel found"
msgstr "לא נמצא ערוץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "No conversation selected."
msgstr "לא נבחרה שיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No conversation yet..."
msgstr "עדיין אין שיחות..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No history messages"
msgstr "אין היסטוריית הודעות"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "No message_id found in context"
msgstr "לא נמצא message_id בהקשר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
msgid "No messages found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "No recipient"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "No recipient found."
msgstr "לא נמצא נמען/ת"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"לא קיבלנו תגובה. נא לבדוק פרטי שרת.\n"
"%s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "No results found"
msgstr " לא נמצאו תוצאות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "No saved templates"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No starred messages"
msgstr "אין הודעות שסומנו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No thread found."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.js:0
msgid "No thread named \"%(thread_name)s\""
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "אין שרשור לתשובות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No user found"
msgstr "לא נמצא משתמש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "No user found that is not already a member of this channel."
msgstr "לא נמצא משתמש שכבר חבר/ה בערוץ זה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "No users found"
msgstr "לא נמצאו משתמשים"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
msgid "Non existing record or wrong token."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "None"
msgstr "אף אחד"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "דוא\"ל מנורמל"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "לא מאומת"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid "Not interested by this?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "הערה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__no_notif
msgid "Nothing"
msgstr "שום דבר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "התראה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "התראת מייל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Notification Item Image"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Notification Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "סוג התראה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__notification_parameters
msgid "Notification parameters"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "התראה: מחיקת התראות ישנות מ-6 חודשים"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Notify scheduled messages"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.actions.client,name:mail.discuss_notification_settings_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model:ir.ui.menu,name:mail.menu_notification_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "התראות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications allowed"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications blocked"
msgstr "התראות נחסמו"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Notify Recipients"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "Notify everyone"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_count
msgid ""
"Number of days/week/month before executing the action after or before the "
"scheduled plan date."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"מספר הימים/שבועות/חודשים לפני ביצוע הפעולה. זה מאפשר לתכנן זמן יעד לפעולה."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will not send notifications on this device."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will send notifications on this device!"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Offline"
msgstr "לא מקוון"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Offline - %(offline_count)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "ערך יש - תו"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "ערך ישן - תאריך-שעה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "ערך מספרי עם נקודה עשרונית ישן"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "ערך מספרי ישן"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "ערך טקסט ישן"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "ערכים ישנים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr "אחרי שהודעה קיבלה כוכב, אפשר לבוא לכאן ולבדוק מה קורה בכל זמן."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.xml:0
msgid "Ongoing call"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Online"
msgstr "מקוון"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Online - %(online_count)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators are allowed to export mail message"
msgstr "רק מנהלים מורשים לייצא הודעות מייל"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators can modify 'model' and 'res_id' fields."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Only custom models can be modified."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Only members of %(group_name)s group are allowed to edit templates "
"containing sensible placeholders"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Only messages type comment can have their content updated"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
msgid "Open"
msgstr "פתח"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
msgid "Open Actions Menu"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
msgid "Open Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Open Discuss App"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "פתיחת מסמך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Open Form View"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Open Link"
msgstr "פתיחת קישור"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Open card"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
msgid "Open in Discuss"
msgstr "פתיחה בדיון"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_view.xml:0
msgid "Open preview in a separate window."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Operation not supported"
msgstr "הפעולה אינה נתמכת"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "בוטל"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"מזהה אפשרי של שרשור (רשומה) שאליו יצורפו כל ההודעות הנכנסות, גם אם לא השיבו "
"אליו. אם מוגדר, הדבר יבטל את יצירת הרשומות החדשות לחלוטין."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"שפת תרגום אופציונלית (קוד ISO) לבחירה בעת שליחת אימייל. אם לא מוגדר, הגרסה "
"האנגלית תשמש. בדרך כלל זה צריך להיות ביטוי מציין מיקום המספק את השפה "
"המתאימה, למשל. {{ object.partner_id.lang }}."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Options"
msgstr "מוצרים אופציונליים"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"דיון מקורי: תשובות הולכות לשרשור המסמך המקורי.\n"
"\n"
"כתובת מייל אחרת: תשובות הולכות לכתובת המייל שעודכנה במספר מעקב של ההודעה, במקום לדיון על מסמך המקור.\n"
"זה משפיע על קביעת מספר הזיהוי (message-id) של ההודעה."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Original message was deleted"
msgstr "ההודעה המקורית נמחקה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Original note:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
msgid "Other activities"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr " יוצא"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "שרת דואר יוצא"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "שרת דואר יוצא"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "דוא\"ל יוצא"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "שרת דואר יוצא"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Overdue"
msgstr "איחור"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "דריסת מייל היוצר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Overwrite Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "שרת POP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "שרתי POP/IMAP"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets received:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets sent:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "אב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__parent_channel_id
msgid "Parent Channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "הודעת אב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "מודל אב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "מזהה רשומת שרשור אב "

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__parent_channel_id
msgid "Parent channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"מודל אב המחזיק בכינוי. המודל המחזיק במזהה לכינוי אינו בהכרח המודל שניתן על "
"ידי alias_model_id (דוגמה: project (parent_model) ומשימה (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "Participant avatar"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_push_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "לקוח/ספק"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "Partner Profile"
msgstr "פרופיל שותף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "לקוחות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "סיסמה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Pause"
msgstr "השהה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__payload
msgid "Payload"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "טלפון"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "שיחת טלפון"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Pick a specific time"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Pick an Activity Plan to launch"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Pin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Pin It"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
msgid "Pinned Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "תוכנית"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date
msgid "Plan Date"
msgstr "תאריך התוכנית"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "שם התוכנית"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_summary
msgid "Plan Summary"
msgstr "סיכום תוכנית"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "סיכום תוכנית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
msgid "Planned"
msgstr "מתוכנן"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Planned Activities"
msgstr "פעילויות מתוכננות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "מתוכנן בעוד"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "תכנון"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Play"
msgstr "הפעלה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Please complete customer's information"
msgstr "נא למלא את פרטי הלקוח"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "נא ליצור עמנו קשר במקום להשתמש ב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Please wait while the file is uploading."
msgstr "נא להמתין בזמן שהקובץ עולה."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"מדיניות שליחת הודעה במסמך באמצעות שער הדואר.\n"
"- כולם: כולם יכולים לשלוח\n"
"- לקוחות/ספקים: רק לקוחות/ספקים מאומתים\n"
"- עוקבים: רק עוקבים של המסמך הקשור או חברים בערוצים הבאים\n"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Pop out Attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Granted"
msgstr "ניתנה גישה לפורטל"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Revoked"
msgstr "הוסרה הגישה לפורטל"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "פרסום במסמך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Post your message on the thread"
msgstr "פרסום הודעה בשרשור"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "מופעל ע\"י"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "פעילויות קודמות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "כתובת מועדפת למענה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "Press Enter to start"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Preview my camera"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "תצוגה מוקדמת של"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "סוג פעולה קודמת"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "פרטיות"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
msgid "Processing"
msgstr "מעבד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Public Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Public Channels"
msgstr "ערוצים ציבוריים"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "הסכם אחריות מפרסם"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_push_device
msgid "Push Notification Device"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_push
msgid "Push Notifications"
msgstr "הודעות בדחיפה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push to Talk"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Push to talk"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "קיצור לדיבור-בלחיצה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push-to-talk key"
msgstr "מקש דיבור-בלחיצה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search…"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "RTC Session ID:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Raise Hand"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "אורחים מגיבים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "שותפים מגיבים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "תגובות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "תאריך קריאה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read Less"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read More"
msgstr "קרא עוד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
msgid "Ready"
msgstr "מוכן"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "מוכן לשליחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/bus_connection_alert.xml:0
msgid "Real-time connection lost..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "סיבה"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "התקבל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Recent"
msgstr "אחרון"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "נמען"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "נמענים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "סוג פעולה מומלצת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "רשומה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "שם רשומה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "מזהה רשומת שרשור"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "מזהים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Refuse"
msgstr "דחה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "בברכה,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Register new key"
msgstr "רישום מקש חדש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Reject"
msgstr "דחה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "חברה מקושרת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "מזהה מסמך קשור"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__res_id
msgid "Related Document Id"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "דגם מסמך קשור"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "שם מודל למסמך מקושר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "תבנית מייל מקושר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "הודעה קשורה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "לקוח/ספק מקושר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "שדה הקשר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
msgid "Remove"
msgstr "הסר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Remove Blur"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "הסר עוקבים"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Remove address from blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "הסרת המייל מאשף הרשימה ההשחורה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Remove this follower"
msgstr "הסר עוקב זה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Rename Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "דגם עיבוד"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Repeat"
msgstr "חזור"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "תגובות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Reply"
msgstr "השב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "השב ל"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr "כתובת אימייל לתשובה. הגדרת reply_to עוקפת את יצירת השרשור האוטומטית."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "השב ל-"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Replying to"
msgstr "במענה ל"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Report"
msgstr "דוח"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "מבקש שותף"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "שלח שוב"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "אשף שליחה מחדש"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "אישור איפוס"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "אחראי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "הגבל את עיבוד התבניות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "נסה שוב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Revert"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "צפה בכל תבניות הדוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "תוכן מכיל טקסט-עשיר"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "הודעת HTML/טקסט-עשיר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "שרת SMTP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "איש מכירות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Save"
msgstr "שמור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Save as Template"
msgstr "שמור כתבנית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Save editing"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "תיזמון"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr ""

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_model.js:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity"
msgstr "תזמן פעילות"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity On Selected Records"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Note"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule activities to help you get things done."
msgstr "קבע פעילויות שיעזרו לך לגרום לדברים לעבוד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Schedule activity"
msgstr "תזמן פעילות"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Schedule an Activity"
msgstr "תזמן פעילות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity"
msgstr "תזמן פעילות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity on selected records"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_cron
msgid "Scheduled Actions"
msgstr "פעולות מתוזמנות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "תאריך מתוזמן"

#. module: mail
#: model:ir.model,name:mail.model_mail_scheduled_message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Scheduled Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "תזמון מועד שליחה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "תסריט"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Search"
msgstr "חיפוש"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "חיפוש כינוי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "חפש בקבוצות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "חפש שרתי דואר נכנס"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
msgid "Search Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Search Messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Search More..."
msgstr "חפש עוד..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search Sub Channels"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search button"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search by name"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search for a GIF"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a channel..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a user..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search in progress"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search..."
msgstr "חיפוש…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
msgid "Search: %s"
msgstr "חיפוש: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Email Changed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Login Changed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Password Changed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "See all pinned messages."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s and %(user2)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s and %(user3)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and 1 other"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by everyone"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.xml:0
msgid "Seen by:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Select a user..."
msgstr "בחירת משתמש/ת..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Select duration"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Send"
msgstr "שלח"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "שלח דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Send Later"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Send Mail (%s)"
msgstr "שלח דוא\"ל: (%s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr "שלח כעת"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Send a message to followers…"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "שלח דוא\"ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Send message"
msgstr "שלח הודעה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "מייל השולח"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Sent"
msgstr "נשלח"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "רצף"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "שרת וכניסת משתמשים"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "פעולת שרת"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "פרטי שרת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "שם השרת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "עדיפות השרת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "סוג השרת"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "Server error"
msgstr "שגיאת שרת"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"השרת השיב עם ההחרגה הבאה:\n"
"%s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "סוג שרת IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "סוג שרת POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "שנו את הגדרת הפעילות ל לא-נכון כדי להחביא את הערוץ מבלי להסיר אותו."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "הגדרות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Share Screen"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Shared canned responses"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Shared with all users."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__source
msgid "Shortcut"
msgstr "קיצור דרך"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Show Followers"
msgstr "הצגת עוקבים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Show a helper message"
msgstr "הצגת הודעה עזרה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
msgid "Show activities"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.xml:0
msgid "Show all recipients"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show less"
msgstr "הצגת פחות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show more"
msgstr "הצגת יותר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Show sidebar"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Show video participants only"
msgstr "הצג מתשתתפים בעלי וידאו בלבד"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Showing"
msgstr "מציג"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "פעולת תפריט-צד"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Snooze 7d"
msgstr "נמנום 7 ימים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "So uhh... maybe go favorite some GIFs?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "מקור"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "משתמש ספציפי"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
msgid "Starred"
msgstr "מסומן בכוכב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Call"
msgstr "התחל שיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Start a Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Video Call"
msgstr "התחל בשיחת הוידאו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a conversation"
msgstr "להתחיל שיחה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_sidebar_patch.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a meeting"
msgstr "התחל פגישה "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "סטטוס"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "סטטוס"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "סטטוס זמן"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
msgid "Status with time"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Stay tuned! Enable push notifications to never miss a message."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "כמות צעדים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Stop Recording"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop Sharing Screen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop camera"
msgstr "כבה מצלמה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Stop replying"
msgstr "להפסיק לענות"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sub_channel_ids
msgid "Sub Channels"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "נושא"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "נושא (אפשר להשתמש כאן בתבניות)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Subject:"
msgstr "נושא:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__substitution
msgid "Substitution"
msgstr "תחליף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "תת סוג"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "תתי סוגים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "הצעה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "הצע את הפעילות הבאה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "הצעות לפעולות אלה כשהפעולה הנבחרת הסתיימה."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "תקציר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "סיכום:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "פרמטר מערכת"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "הודעת מערכת"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Tab to select"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "מודל יעד"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "ח.פ / ע.מ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Technical Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "תבנית"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template Description"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_name
msgid "Template Name"
msgstr "שם תבנית"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "תצוגה מקדימה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "שפת תצוגה מקדימה לתבנית"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Template creation from composer requires a valid model."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering should only be called with a list of IDs. Received "
"“%(res_ids)s” instead."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "תבניות"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "בדיקה ואישור"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "ה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "The 'Due Date In' value can't be negative."
msgstr "ה'תאריך יעד' לא יכול להיות שלילי."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
msgid ""
"The 'To-Do' activity type is used to create reminders from the top bar menu "
"and the command palette. Consequently, it cannot be archived or deleted."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
msgid "The Fullscreen mode was denied by the browser"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_extension_service.js:0
msgid ""
"The Push-to-Talk feature is only accessible within tab focus. To enable the "
"Push-to-Talk functionality outside of this tab, we recommend downloading our"
" %(anchor_start)sextension%(anchor_end)s."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The activity cannot be launched:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "הקובץ המצורף %s לא קיים או שאין לך הרשאות אליו."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "The attachment %s does not exist."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.js:0
msgid "The avatar has been updated!"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "The conversation is empty."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "The duration of voice messages is limited to 1 minute."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "המייל נשלח ל"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
msgid "The email template(s) have been restored to their original settings."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_push_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "המשתמש הפנימי הממונה על איש קשר זה."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "ההודעה הנ\"ל לא התקבלה בהצלחה בכתובת"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"ההודעה הנ\"ל לא התקבלה בהצלחה בכתובת %(alias_display_name)s.\n"
"רק %(contact_description)s מורשים לשלוח הודעות לכתובת זו.<br /><br />\n"
"אנא וודאו כי אתם משתמשים בכתובת הנכונה או נסו לפנות אלינו בכתובת %(default_email)s במקום."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"ההודעה הנ\"ל לא התקבלה בהצלחה בכתובת %(alias_display_name)s.\n"
"אנא נסו שנית מאוחר יותר או פנו ל%(company_name)s במקום."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"The message scheduled on %(model)s(%(id)s) with the following content could "
"not be sent:%(original_message)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__from_message_id
msgid "The message the channel was created from."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"המודל (סוג מסמך Odoo) שאליו הכינוי הזה תואם. כל דוא\"ל נכנס שלא יענה לרשומה "
"קיימת יביא ליצירת רשומה חדשה של מודל זה (למשל משימת פרויקט)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"שם כינוי הדוא\"ל, למשל 'עבודות' אם ברצונך לקבל הודעות דוא\"ל ל "
"<<EMAIL>>"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" has been started"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The records must belong to the same company."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_message.py:0
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %(type)s, Operation: %(operation)s)\n"
"\n"
"Records: %(records)s, User: %(user)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "The server \"%s\" cannot be used because it is archived."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "The subscription preferences were successfully applied."
msgstr "הגדרות המנוי עודכנו בהצלחה."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "זה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action can only be done on a mail thread models"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action cannot be done on transient models."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "This action will send an email."
msgstr "פעולה זו תשלח מייל."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This channel doesn't have any attachments."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This channel doesn't have any pinned messages."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "This channel has no thread yet."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This conversation doesn't have any attachments."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This conversation doesn't have any pinned messages."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "דוא\"ל זה מופיע ברשימה השחורה לצורך דיוור המוני. לחץ לביטול ההופעה שם."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "השדה הזה מבחין בין אותיות קטנות וגדולות"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"שדה זה משמש לחיפוש בכתובת דוא\"ל שכן שדה הדוא\"ל הראשי יכול להכיל יותר מרק "
"את כתובת הדוא\"ל."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "This layout seems to no longer exist."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message_model.js:0
msgid "This message has already been sent."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
msgid "This record has an exception activity."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid ""
"This setting will be applied to all channels using the default notification "
"settings."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__channel_notifications
msgid ""
"This setting will only be applied to channels. Mentions only if not "
"specified."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "שרשור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread Image"
msgstr "דימוי שרשור"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread has unread messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Thread image"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/thread_actions.js:0
msgid "Threads"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_tz
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "אזור זמן"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "תואר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "To"
msgstr "ל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "אל (דוא\"ל)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "ל (שותפים)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
msgid "To :"
msgstr "ל:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "To peer:"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "To:"
msgstr "ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Today"
msgstr "היום"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Today at %(time)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Today:"
msgstr "היום:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Tomorrow"
msgstr "מחר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Afternoon"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Morning"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Tomorrow:"
msgstr "מחר:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "נושאים לדיון בקבוצה זו..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "מעקב"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "מספר מעקב"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "מספר מעקב"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "מספר מעקב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Translate"
msgstr "תרגם"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Translation Failure"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_from
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "הפעלה"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "הפעל את הפעילות הבאה"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Turn camera on"
msgstr "הדלקת מצלמה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Turn on notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "סוג"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "סוג עיכוב"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Type the name of a person"
msgstr "הקלד את שם האיש קשר"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid "Unable to connect to SMTP Server"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "אין אפשרות לשלוח הודעה, אנא הגדר את כתובת הדוא\"ל של השולח."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Unable to send message, please configure the sender's email address."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign from me"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "הסרה מרשימה שחורה"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Undeafen"
msgstr "ביטול השתקה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Unfollow"
msgstr "בטל מעקב"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Unit"
msgstr "יחידה"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "יחידת עיכוב"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
msgid "Unknown"
msgstr "לא ידוע"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
msgid "Unknown error"
msgstr "שגיאה לא ידועה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Unknown error: %(error)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Unmute"
msgstr "בטל השתקה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Unmute Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Unpin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Conversation"
msgstr "הסרה מהודעות חשובות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Unpin Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__unpin_dt
msgid "Unpin date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "מספר ההודעות שלא נקראו"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "הודעות שלא נקראו"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Unselect person"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Unstar all"
msgstr "הסרת כוכב מהכל"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Unsupported report type %s found."
msgstr "לא נמצאה תמיכה בדוח מסוג %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until I turn it back on"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Untitled"
msgstr "ללא כותרת"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Update Template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Upload Avatar"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "העלאת מסמך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload File"
msgstr "טען קובץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload file"
msgstr "העלאת קובץ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Upload:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploaded"
msgstr "הועלה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploading"
msgstr "מעלה"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "השתמש בשרתי דוא\"ל מותאמים אישית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Use Default"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "השתמש בשרתי Twilio ICE"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "השתמש בשרת Gmail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Use a local script to fetch your emails and create new records."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid ""
"Use default from user settings if not specified. This setting will only be "
"applied to channels."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Use message_notify to send a notification to an user."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "השתמש בתבנית"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "שימוש בתכונה דיבור-בלחיצה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "משמש ב"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "משמש להצגת המטבע במעקב אחרי ערכים כספיים"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "משמש לסידור תתי-סוגים"

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "משתמש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "נוכחות משתמש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "הגדרות משתמש"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "הגדרות משתמש"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "התראות ייעודיות למשתמש"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "סוג משתמש"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is a bot"
msgstr "המשתמש הנו רובוט"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is idle"
msgstr "המשתמש לא פעיל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is offline"
msgstr "משתמש לא מחובר"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is online"
msgstr "המשתמש מחובר"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "שם משתמש"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "תקף"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Video Settings"
msgstr "הגדרות וידאו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Video player:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
msgid "View"
msgstr "תצוגה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "View %s"
msgstr "צפה ב%s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "View Profile"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "View Reactions"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
msgid "View Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "סוג תצוגה"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "View all activities"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "View or join channels"
msgstr "צפיה או הצטרפות לערוצים"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "קול"

#. module: mail
#: model:ir.ui.menu,name:mail.menu_call_settings
msgid "Voice & Video"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.discuss_call_settings_action
msgid "Voice & Video Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice Detection"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Voice Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice detection threshold"
msgstr "סף קליטת קולות"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "Voice recording stopped"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "נפח"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "עוצמת קול לשותף"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "עוצמת קול לשאר השותפים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "Warning"
msgstr "אזהרה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "We were not able to fetch value of field '%(field)s'"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "שבועות"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "What's your name?"
msgstr "מה שמך?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__user_tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"האם להשאיר עותק מלא של כל מייל לצורך גיבוי וחיבור כל צרופה לכל הודעה שעבר "
"עיבוד. לרוב זה יכפיל את הגודל של מאגר הנתונים של המיילים."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"האם להוריד צרופות. אם לא מסמנים אפשרות זו אז המיילים הנכנסים לא יעברו עם "
"הצרופות לתהליך העיבוד."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Write /field to insert dynamic content"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Write Feedback"
msgstr "כתוב משוב"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Write your message here..."
msgstr "כתיבת הודעה כאן..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Wrong operation name (%s)"
msgstr "מספר פעולה שגוי (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yeah, pin it!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Yes"
msgstr "כן"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yes, remove it please"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Yesterday"
msgstr "אתמול"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Yesterday at %(time)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Yesterday:"
msgstr "אתמול:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"יציאה מהשיחה הקבוצתית הזו לגרום לכך שלא תהיה לך גישה יותר אלא אם יזמינו אותך"
" מחדש. האם באמת ברצונך להמשיך?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "You are no longer following \"%(thread_name)s\"."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"You are not allowed to change the target record of a scheduled message."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "You are not allowed to send this scheduled message"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload an attachment here."
msgstr "אינך רשאי להעלות כאן קובץ מצורף."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "הנך מנהל הערוץ הזה. באמת ברצונך לעזוב?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr "אפשר לסמן כל הודעה ב'כוכב', והיא תופיע בתיבת הדואר הזו."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "You can not write on %(field_name)s."
msgstr "אפשר לכתוב על %(field_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with existing users."
msgstr "אפשר לפתוח צ'אט רק עם משתמשים קיימים."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with partners that have a dedicated user."
msgstr "אפשר לפתוח צ'אט רק עם שותפים שיש להם משתמש מקושר."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr "אין לך הרשאה להסיר מיילםי מהרשיהמ השחורה. נא ליצור קשר עם המנהל/ת."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "You have been assigned to %s"
msgstr "הוקצת ל%s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "הוקצת ל"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js:0
msgid "You have been invited to #%s"
msgstr "הוזמנת ל#%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr "אפשר להוסיף קבצים לתבנית זו, שיצורפו לכל המיילים שיווצרו מתבנית זו"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "You may not define a template on an abstract model: %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned %(conversation_name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned your conversation with %(user_name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unsubscribed from %s."
msgstr "הסרת מנוי מ-%s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a chat!"
msgstr "הוזמנת לצ'אט!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a meeting!"
msgstr "הוזמנת לפגישה !"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "You:"
msgstr "את/ה:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "שלך"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account login has been updated"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account password has been updated"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your browser does not support videoconference"
msgstr "הדפדפן שלך לא תומך בשיחות וידאו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support voice activation"
msgstr "הדפדפן שלך לא תומך בהפעלה קולית"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support webRTC."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Your inbox is empty"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your name"
msgstr "השם שלך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
msgid "[%(name)s] (no email address)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "addresses linked to registered partners"
msgstr "כתובות קשורות לשותפים רשומים"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "לאחר תאריך הסיום"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "לאחר תאריך יעד הפעילות הקודמת"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "afternoon"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "alias"
msgstr "כינוי"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "alias %(name)s: %(error)s"
msgstr "כינוי %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "available bitrate:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "back"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "על ידי"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "camera"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "channels"
msgstr "ערוצים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "clock rate:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "codec:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "created this channel."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "custom"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__days
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "ימים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days overdue:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "deaf"
msgstr "מושתק"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "default"
msgstr "ברירת מחדל"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__mail_push_device_id
msgid "devices"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "document"
msgstr "מסמך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "בוצע"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down ICE:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "לדוג' \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "לדוג' 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "לדוג'  ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Discuss Proposal"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "למשל: לדון בהצעה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
msgid "e.g. Log a note"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "למשל הכשרה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "למשל: קבע.י פגישה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "לדוג' תמיכה"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g: Send order confirmation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "for"
msgstr "ל"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "נוצר מ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "השתנה מ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "in a few seconds"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias"
msgstr "כינוי לא הוגדר נכון"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias (unknown reference record)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "invited %s to the channel"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "joined the channel"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "left the channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list-item"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "live"
msgstr "חי"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "media player Error"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "microphone"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "model %s does not accept document creation"
msgstr "מודל %s לא מאפשר יצירת מסמכים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "monday"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__months
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "חודשים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "morning"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "ms"
msgstr "מילישניות ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "muted"
msgstr "מושתק"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "new"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "no connection"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "now"
msgstr "עכשיו"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "on"
msgstr "ב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "on:"
msgstr "ב"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
msgid "or"
msgstr "או"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "or press %(send_keybind)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "other members."
msgstr "משתתפים אחרים."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
msgid "props.action.title"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "raising hand"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to followers"
msgstr "מוגבל לעוקבים"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to known authors"
msgstr "מוגבלים ליוצרים מוכרים"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "results out of"
msgstr "תוצאות מתוך"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "screen"
msgstr "מסך"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "some specific addresses"
msgstr "כמה כתובות ספציפיות"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "target model unspecified"
msgstr "מודל יעד לא הוגדר"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "צוות."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "template"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown error"
msgstr "שגיאה לא ידועה"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown target model %s"
msgstr "מודל יעד לא ידוע: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up ICE:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "users"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "view"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__weeks
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "שבועות"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "your alias"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr ""
