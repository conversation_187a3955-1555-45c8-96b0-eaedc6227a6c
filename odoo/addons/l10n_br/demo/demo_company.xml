<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_br" model="res.partner" forcecreate="1">
        <field name="name">BR Company</field>
        <field name="vat">51494569013170</field>
        <field name="street">Praça Mauá 1</field>
        <field name="city">Rio de Janeiro</field>
        <field name="country_id" ref="base.br"/>
        <field name="state_id" ref="base.state_br_rj"/>
        <field name="zip">20081-240</field>
        <field name="phone">+55 11 96123-4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.brexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_br" model="res.company" forcecreate="1">
        <field name="name">BR Company</field>
        <field name="partner_id" ref="base.partner_demo_company_br"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_br')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_br'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>br</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_br')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
