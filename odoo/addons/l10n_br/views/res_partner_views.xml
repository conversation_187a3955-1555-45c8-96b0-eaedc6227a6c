<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="br_partner_address_form" model="ir.ui.view">
            <field name="name">partner.form.address.extended</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="900"/>
            <field name="arch" type="xml">
                <form>
                    <div class="o_address_format">
                        <field name="country_enforce_cities" invisible="1"/>
                        <field name="parent_id" invisible="1"/>
                        <field name="type" invisible="1"/>
                        <field name="street" placeholder="Street..." class="o_address_street oe_read_only"
                               readonly="type == 'contact' and parent_id"/>
                        <div class="oe_edit_only o_row">
                            <field name="street_name" placeholder="Street" style="flex: 3 1 auto"
                                   readonly="type == 'contact' and parent_id"/>
                            <span> </span>
                            <field name="street_number" placeholder="Street #" style="flex: 1 1 auto"
                                   readonly="type == 'contact' and parent_id"/>
                            <span> - </span>
                            <field name="street_number2" placeholder="Complement" style="flex: 1 1 auto"
                                   readonly="type == 'contact' and parent_id"/>
                        </div>
                        <field name="street2" placeholder="Neighborhood" class="o_address_street"
                               readonly="type == 'contact' and parent_id"/>
                        <field name="city_id"
                               placeholder="City"
                               class="o_address_city"
                               domain="[('country_id', '=', country_id)]"
                               invisible="not country_enforce_cities"
                               readonly="type == 'contact' and parent_id"
                               context="{'default_country_id': country_id, 'default_state_id': state_id, 'default_zipcode': zip}"/>
                        <field name="city"
                               placeholder="City"
                               class="o_address_city"
                               invisible="country_enforce_cities and (city_id or city in ('', False))"
                               readonly="type == 'contact' and parent_id"/>
                        <field name="state_id"
                               class="o_address_state"
                               placeholder="State"
                               readonly="type == 'contact' and parent_id"
                               options="{'no_open': True, 'no_quick_create': True}"
                               context="{'default_country_id': country_id}"/>
                        <field name="zip" placeholder="ZIP" class="o_address_zip"
                               readonly="type == 'contact' and parent_id"/>
                        <field name="country_id"
                               placeholder="Country"
                               class="o_address_country"
                               readonly="type == 'contact' and parent_id"
                               options="{'no_open': True, 'no_create': True}"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="br_partner_tax_fields_form" model="ir.ui.view">
            <field name="name">res.partner.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="account.view_partner_property_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='vat']" position="after">
                    <field name="l10n_br_ie_code" invisible="'BR' not in fiscal_country_codes"/>
                    <field name="l10n_br_im_code" invisible="'BR' not in fiscal_country_codes"/>
                    <field name="l10n_br_isuf_code" invisible="'BR' not in fiscal_country_codes"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
