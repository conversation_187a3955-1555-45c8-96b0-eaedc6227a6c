# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON>il O<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Option A. Pairing Code</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box. You can also find it on the IoT Box Homepage<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>\n"
"                    3. After some time the IoT Box should appear in the list along with the connected devices.<br/>"
msgstr ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>ตัวเลือก A. รหัสการจับคู่</strong><br/>\n"
"                    1. อ่านรหัสการจับคู่จากจอแสดงผลหรือเครื่องพิมพ์ความร้อนที่เชื่อมต่อกับ IoT Box คุณสามารถค้นหารหัสการจับคู่ได้จากหน้าแรกของ IoT Box<br/>\n"
"                    2. กรอกรหัสด้านล่างนี้และคลิก \"จับคู่\"<br/>\n"
"                    3. หลังจากนั้นสักครู่ IoT Box ควรจะปรากฏในรายการพร้อมกับอุปกรณ์ที่เชื่อมต่อ<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box and connect it to the internet through ethernet"
msgstr "0. เปิดกล่อง IoT และเชื่อมต่อกับอินเทอร์เน็ตผ่านทางอีเธอร์เน็ต"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<br/>\n"
"                    1. Copy the token below<br/>\n"
"                    2. While on the same network as the IoT Box open a web browser and go to the IoT Box ip address<br/>\n"
"                    3. In the \"Server\" section click on \"Configure\" button<br/>\n"
"                    4. Paste the token in the \"Server token\" section and click on \"Connect\" button<br/>\n"
"                    5. After some time the IoT Box should appear in the list along with the connected devices<br/>"
msgstr ""
"<br/>\n"
"                    1. คัดลอกโทเค็นด้านล่าง<br/>\n"
"                    2. ในขณะที่อยู่ในเครือข่ายเดียวกันกับ IoT Box ให้เปิดเว็บเบราว์เซอร์และไปที่ที่อยู่ IP ของ IoT Box<br/>\n"
"                    3. ในส่วน \"เซิร์ฟเวอร์\" คลิกที่ปุ่ม \"กำหนดค่า\"<br/>\n"
"                    4. วางโทเค็นในส่วน \"โทเค็นเซิร์ฟเวอร์\" และคลิกปุ่ม \"เชื่อมต่อ\"<br/>\n"
"                    5. หลังจากนั้นสักครู่ IoT Box ควรจะปรากฏในรายการพร้อมกับอุปกรณ์ที่เชื่อมต่อ<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr "<span class=\"badge text-bg-secondary\">ตัดการเชื่อมต่อแล้ว</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr "<span class=\"badge text-bg-success\">เชื่อมต่อแล้ว</span>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "เพิ่มโปรแกรมกล่อง IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการรีสตาร์ท Odoo บนกล่อง IoT?"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "อัพเดตไดรเวอร์อัตโนมัติ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "อัปเดตไดรเวอร์โดยอัตโนมัติเมื่อ IoT Box บูท"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "เครื่องสแกนบาร์โค้ด"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "บลูทูธ"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "กล้อง"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr "ตรวจสอบการเชื่อมต่อกล่อง IoT ลองรีสตาร์ทหากจำเป็น"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr "ตรวจสอบว่าอุปกรณ์ยังเชื่อมต่ออยู่หรือไม่"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr "คลิกที่นี่เพื่อเปิดหน้าแรก IoT ของคุณ"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "คลิกที่ ขั้นสูง/แสดงรายละเอียด/รายละเอียด/ข้อมูลเพิ่มเติม"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"คลิกที่ ให้ดำเนินการ .../เพิ่มข้อยกเว้น/เยี่ยมชมเว็บไซต์นี้/ไปที่หน้าเว็บ"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "คลิกที่"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr "ปิด"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr "ปิดหน้าต่างและลองใหม่อีกครั้ง"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "บริษัท"

#. module: iot
#: model:ir.ui.menu,name:iot.view_iot_selected_printer_local_action
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr "เชื่อมต่อ"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect my IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "การเชื่อมต่อ"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr "การเชื่อมต่อกับกล่องไอโอทีล้มเหลว"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr "การเชื่อมต่อกับอุปกรณ์ล้มเหลว"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Connection to printer failed "
msgstr "การเชื่อมต่อกับเครื่องพิมพ์ล้มเหลว"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__device_ids
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "อุปกรณ์"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "จำนวนอุปกรณ์"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "ประเภทอุปกรณ์"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "ประเภทอุปกรณ์คือ #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "อุปกรณ์"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "แสดง"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_device_ids
msgid "Display Device"
msgstr "อุปกรณ์แสดงผล"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "URL ที่แสดง"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "ที่อยู่โดเมน"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr "ดาวน์โหลดบันทึก"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr "ดาวน์โหลดบันทึก"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Failed to send to printer."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Firefox เท่านั้น: คลิกยืนยันข้อยกเว้นด้านความปลอดภัย"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "โมดูลข้อมูลทางการเงิน"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "HDMI"
msgstr "HDMI"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__id
msgid "ID"
msgstr "ไอดี"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "อุปกรณ์ไอโอที"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "ตัวระบุ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "ตัวระบุ (ที่อยู่ Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "หากอุปกรณ์เชื่อมต่อกับกล่อง IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"หากคุณอยู่บนเซิร์ฟเวอร์ที่ปลอดภัย (HTTPS) "
"ให้ตรวจสอบว่าคุณยอมรับการรับรองหรือไม่:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "เวอร์ชันรูปภาพ"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "ไอโอที"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "กล่อง IoT "

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "หน้าแรกของกล่อง IoT"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "กล่อง IoT"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "อุปกรณ์ไอโอที"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_ids
msgid "IoT Devices"
msgstr "อุปกรณ์ไอโอที"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "เป็น Scanner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__is_websocket_active
msgid "Is Websocket active?"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "คีย์บอร์ด"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "รูปแบบแป้นพิมพ์"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__label_printer
msgid "Label Printer"
msgstr "เครื่องพิมพ์ป้ายกำกับ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "ค่าที่ส่งล่าสุด"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr "กระบวนการรีสตาร์ทครั้งล่าสุดยังไม่เสร็จสิ้น"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "เค้าโครง"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "การวัดด้วยตัวเอง"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "อ่านค่าการวัดจากตัวเครื่องด้วยตนเอง"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "สลับประเภทอุปกรณ์ระหว่างแป้นพิมพ์และสแกนเนอร์ด้วยตนเอง"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "ผู้ผลิต"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "ชื่อ"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "เครือข่าย"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "New IoT Box connected!"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr "ไม่พบกล่อง IoT!"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "No new IoT Box found."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "No printer selected"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "No report to display"
msgstr "ไม่มีรายงานที่จะแสดง"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo ไม่สามารถเข้าถึงกล่อง IoT ได้"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__office_printer
msgid "Office Printer"
msgstr "เครื่องพิมพ์สำนักงาน"

#. module: iot
#. odoo-python
#: code:addons/iot/models/ir_actions_report.py:0
msgid ""
"One of the printer used to print document have been removed. Please retry "
"the operation to choose new printers to print."
msgstr ""
"เครื่องพิมพ์เครื่องหนึ่งที่ใช้ในการพิมพ์เอกสารถูกลบออก "
"โปรดลองดำเนินการอีกครั้งเพื่อเลือกเครื่องพิมพ์ใหม่ที่จะพิมพ์"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Option B. Connection Token"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "จับคู่"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "รหัสการจับคู่"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "สถานีการชำระเงิน"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser):"
msgstr ""
"โปรดยอมรับใบรับรองของกล่อง IoT ของคุณ "
"(ขั้นตอนจะขึ้นอยู่กับเบราว์เซอร์ของคุณ):"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr "โปรดตรวจสอบว่ากล่องไอโอทียังคงเชื่อมต่ออยู่หรือไม่"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Please enter a pairing code."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr "กรุณารอสักครู่"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Print"
msgstr "พิมพ์"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "เครื่องพิมพ์"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "รายงานเครื่องพิมพ์"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Printers"
msgstr "เครื่องพิมพ์"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Printing operation completed on printer %s"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "สัญญาการรับประกันของผู้จัดพิมพ์สำหรับกล่อง IoT"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__receipt_printer
msgid "Receipt Printer"
msgstr "เครื่องพิมพ์ใบเสร็จ"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "การดําเนินการรายงาน"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "รายงาน xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
#: model:ir.ui.menu,name:iot.iot_settings_menu_action
msgid "Reports"
msgstr "รายงาน"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "Reports with linked printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
#: model:ir.ui.menu,name:iot.iot_clear_selected_devices
msgid "Reset Linked Printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr "การรีสตาร์ทล้มเหลว"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr "รีสตาร์ทเสร็จแล้ว"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr "กำลังรีสตาร์ท"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Sales Details"
msgstr "รายละเอียดการขาย"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "เครื่องชั่ง"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "Select at least one printer"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_select_printers_wizard
msgid "Selection of printers"
msgstr "การเลือกเครื่องพิมพ์"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Sending to printer %s..."
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "ซีเรียล"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "สถานะ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__subtype
msgid "Subtype"
msgstr "ประเภทย่อย"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__subtype
msgid "Subtype of device."
msgstr "ประเภทย่อยของอุปกรณ์"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr "สำเร็จ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Technical Information"
msgstr "ข้อมูลทางเทคนิค"

#. module: iot
#: model:ir.model,name:iot.model_iot_channel
msgid "The Websocket Iot Channel"
msgstr "ช่อง Websocket Iot"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"ไม่พบรหัสการจับคู่ที่คุณระบุในระบบของเรา โปรดตรวจสอบว่าคุณกรอกข้อมูลถูกต้อง"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "ไม่มีอุปกรณ์เชื่อมต่อกับกล่อง IoT นี้"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "ไม่มีอุปกรณ์เชื่อมต่อกับกล่อง IoT ของคุณ"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr "หมดเวลาแล้ว"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "โทเค็น"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "ประเภท"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "ประเภทของการเชื่อมต่อ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "ประเภทของอุปกรณ์"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"URL ของเพจที่อุปกรณ์จะแสดง เว้นว่างไว้เพื่อใช้จอแสดงผล POS สำหรับลูกค้า"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Using Pairing Code to connect..."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "ตัวแปร"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr "เกิดปัญหาในการจับคู่กล่อง IoT ของคุณ กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "We're looking for your IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_ids
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr "เมื่อตั้งค่าอุปกรณ์ที่นี่ รายงานจะถูกพิมพ์ผ่านอุปกรณ์นี้บนกล่อง IoT"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "เชื่อมต่อ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "or wifi"
msgstr ""

#. module: iot
#: model:ir.actions.client,name:iot.action_iot_delete_linked_devices_menu
msgid "reset.linked.printers"
msgstr "reset.linked.printers"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "เพื่อเพิ่มกล่อง IoT"

#. module: iot
#: model:ir.model,name:iot.model_ir_websocket
msgid "websocket message handling"
msgstr "การจัดการข้อความของ websocket"
