# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_si
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-26 09:22+0000\n"
"PO-Revision-Date: 2024-06-26 09:22+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_10
msgid "10. Acquisitions of goods from the EU"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_10a
msgid "10a. Supplies of goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_10b
msgid "10b. Custom posts 42 and 43"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_10c
msgid "10c. Deliveries of bl. after storage on recall"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_10d
msgid "10d. Passed st."
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_11
msgid "11. Services received from the EU"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_11
msgid "11. Supplies of goods and services"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_11
msgid "11. Three-way delivery"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_11a
msgid ""
"11a. Supplies of goods and services in Slovenia, of which VAT is charged by "
"the recipient"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_12
msgid "12. Deliveries of goods and services to other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_12
msgid "12. Distance selling"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_12
msgid "12. Real estate (Part 8 or 9)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_13
msgid "13. Assembly"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_13
msgid "13. Other OS (Part 8 or 10)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_13
msgid "13. Sale of goods at a distance"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_14
msgid "14. Assembly and installation of goods in another Member State"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_14
msgid "14. Purchases and acquisitions"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_14
msgid "14. VAT 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_15
msgid "15. Exempt supplies without the right to deduct VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_15
msgid "15. Real estate (Part 14)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_15a
msgid "15a. VAT 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_15b
msgid "15b. VAT 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_16
msgid "16. Other OS (Part 14)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_16
msgid "16. VAT BL 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_17
msgid "17. VAT ST 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_17a
msgid "17a. 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_17b
msgid "17b. 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_17c
msgid "17c. 8% and 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_18
msgid "18. 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_18a
msgid "18a. VAT BL 9.5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_18b
msgid "18b. VAT BL 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_19
msgid "19. 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_19a
msgid "19a. 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_19a
msgid "19a. VAT ST 9.5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_19b
msgid "19b. VAT ST 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_20
msgid "20. Flat-rate compensation 8%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_20
msgid "20. VAT 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_21
msgid "21. At a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_21a
msgid "21a. VAT 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_21b
msgid "21b. VAT 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_22
msgid "22. At a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_22
msgid "22. VAT 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_22a
msgid "22a. At a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_22a
msgid "22a. VAT 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_22b
msgid "22b. VAT 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_23
msgid "23. 22% of acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_23a
msgid ""
"23a. Of the services received from other EU Member States at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24
msgid "24. 9,5% of acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24a
msgid ""
"24a. Of the services received from other EU Member States at the rate of "
"9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24b
msgid ""
"24b. Acquisitions of goods from other EU Member States at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24c
msgid ""
"24c. Of the services received from other EU Member States at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25
msgid ""
"25. On the basis of self-assessment as a recipient of goods and services at "
"a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_25a
msgid "25a. Acquisition of EU goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25a
msgid ""
"25a. On the basis of self-assessment as a recipient of goods and services at"
" a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_25b
msgid "25b. EU services received"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25b
msgid ""
"25b. On the basis of self-assessment as a recipient of goods and services at"
" a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_25c
msgid "25c. Acquisition SLO Article 76a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_25d
msgid "25d. Purchases other for self-taxation"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_26
msgid "26. On the basis of self-assessment of imports"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_31
msgid "31. Purchases of goods and services"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_31a
msgid ""
"31a. Purchases of goods and services in Slovenia, of which the recipient "
"charges VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_32
msgid "32. Acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_32a
msgid "32a. Services received from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_33
msgid ""
"33. Exempt purchases of goods and services and exempt acquisitions of goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_34
msgid "34. Purchase value of real estate"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_35
msgid "35. Cost of other fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_dobave_3_svet_s_pravico_do_odb_ddv
msgid "3rd world deliveries with right of withdrawal VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_41
msgid ""
"41. From purchases of goods and services, acquisition of goods and services "
"received from other EU Member States and from imports at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_42
msgid ""
"42. From purchases of goods and services, acquisition of goods and services "
"received from other EU Member States and from imports at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_42a
msgid ""
"42a. From purchases of goods and services, acquisition of goods and services"
" received from other EU Member States and from imports at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_43
msgid "43. Of the flat-rate compensation at the rate of 8%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_51
msgid "51. VAT liability"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_52
msgid "52. VAT surplus"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_7
msgid "7. SLO Value excluding VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_8
msgid "8. Self-taxation of supplies Article 76a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_8a
msgid "8a. Goods and services SLO (except for self employed)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_8b
msgid "8b. Goods and services 3rd world"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_9
msgid "9. No right to deduct VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_9
msgid "9. SLO acquisitions (Art. 76a only)"
msgstr ""

#. module: l10n_si
#: model:ir.model,name:l10n_si.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_si
#: model:account.report.column,name:l10n_si.tax_report_balance
#: model:account.report.column,name:l10n_si.tax_report_ir_balance
#: model:account.report.column,name:l10n_si.tax_report_pd_balance
#: model:account.report.column,name:l10n_si.tax_report_pr_balance
#: model:account.report.column,name:l10n_si.tax_report_rp_balance
msgid "Balance"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_odbitni_ddv
msgid "Deductible VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_oproscen_promet_eu
msgid "EXEMPTED TURNOVER - EU"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_I
msgid "I. Supplies of goods and services (values excluding VAT)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_II
msgid "II. VAT charged"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_III
msgid "III. Purchases of goods and services (values excluding VAT)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_IV
msgid "IV. VAT deduction"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_neodbitni_ddv
msgid "Non-deductible VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_obrnjeno_davcno_breme_osnova
msgid "REVERSE CHARGE - BASE"
msgstr ""

#. module: l10n_si
#: model:account.report,name:l10n_si.tax_report_ir
msgid "Report IR"
msgstr ""

#. module: l10n_si
#: model:account.report,name:l10n_si.tax_report_pd
msgid "Report PD-O"
msgstr ""

#. module: l10n_si
#: model:account.report,name:l10n_si.tax_report_pr
msgid "Report PR"
msgstr ""

#. module: l10n_si
#: model:account.report,name:l10n_si.tax_report_rp
msgid "Report rp_O"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_samoobdavcitev_3_svet_uvoz
msgid "Self-taxation - 3rd world imports"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_samoobdavcitev_eu
msgid "Self-taxation - EU"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_samoobdavcitev_slo_na_podlagi_76_a_clena
msgid "Self-taxation - SLO under Article 76a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_obdavcen_promet
msgid "TAXED TURNOVER"
msgstr ""

#. module: l10n_si
#: model:account.report,name:l10n_si.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_ir_promet_v_sloveniji
msgid "Transport in Slovenia"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_vrednost_oproscene_nabave_osnova
msgid "Value of exempt purchases - base"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pr_vrednost_obdavcenih_nabav_osnova
msgid "Value of taxed purchases - base"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_rp_a3
msgid "a3. Value of supplies of goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_pd_a3
msgid "a3. Value of supplies of goods and services"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_rp_a4
msgid "a4. Value of supplies of goods under customs procedures 42 and 63"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_rp_a5
msgid "a5. Value of triangular supplies of goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_rp_a6
msgid "a6. Value of services provided"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_rp_a7
msgid "a7. Value of deliveries of goods after storage on call"
msgstr ""
